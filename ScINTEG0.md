# ScINTEG 模型的有向无环图 (DAG) 表示

```
输入数据
   |
   v
[细胞级数据预处理]------------------>[基因级数据预处理]
   |                                   |
   v                                   v
[细胞图构建]                        [基因调控网络构建]
   |                                   |
   v                                   v
[Cell Encoder] ----------------------> [Gene Encoder]
   |                                   |
   |                                   |
   v                                   v
[细胞嵌入空间] ------------------> [基因嵌入空间]
                    |                  |
                    v                  v
               [Pathway-约束瓶颈层] <--|
                    |                  |
                    |                  |
   +----------------+                  |
   |                |                  |
   v                v                  v
[基因表达重建]  [时序正则化]       [调控网络推断]
   |                |                  |
   v                v                  v
[重建损失]      [时序一致性损失]   [网络结构损失]
   |                |                  |
   +----------------+------------------+
                    |
                    v
               [多目标联合损失]
                    |
                    v
               [模型优化]
                    |
                    v
               [下游应用]
                    |
     +-------------+-------------+-------------+
     |             |             |             |
     v             v             v             v
[细胞类型识别] [通路活性评分] [基因调控网络] [扰动效应预测]
```

## DAG 详细解释

### 1. 输入数据与预处理

- **细胞级数据预处理**：

  - 处理单细胞RNA-seq原始数据
  - 归一化、对数转换
  - 高变基因筛选
- **基因级数据预处理**：

  - 整合先验调控网络数据
  - 基因注释信息
  - 通路知识整合

### 2. 图构建阶段

- **细胞图构建**：

  - 基于相似性构建KNN图 (源自scNET)
  - 细胞-细胞关系建模
- **基因调控网络构建**：

  - 基于先验知识构建基因关系图
  - 融合差异表达信息

### 3. 编码阶段

- **Cell Encoder**：

  - 来自scNET的双向互信息编码器
  - 捕获细胞间关系
- **Gene Encoder**：

  - 采用CEFCON的图注意力网络
  - 多头注意力机制
  - 捕获基因间调控关系

### 4. 嵌入空间

- **细胞嵌入空间**：

  - 表示细胞表型特征
- **基因嵌入空间**：

  - 表示基因功能特征
  - 注意力权重作为调控强度指标

### 5. 瓶颈层

- **Pathway-约束瓶颈层**：
  - 从ExpiMap借鉴的带掩码线性投影
  - 自适应掩码学习机制
  - 通过掩码整合生物先验知识

### 6. 多分支解码与损失计算

- **基因表达重建**：

  - 重建原始表达矩阵
  - MSE/负二项分布损失
- **时序正则化**（可选）：

  - 时序注意力层
  - 保持发育轨迹一致性
- **调控网络推断**：

  - 内积解码器 (来自scNET)
  - 估计调控关系概率

### 7. 多目标联合损失

- 整合多个损失函数:
  - 表达重建损失
  - 网络结构损失
  - 掩码惩罚项
  - 时序一致性损失（可选）

### 8. 下游应用

- **细胞类型识别**：精确细胞类型注释，包含不确定性量化
- **通路活性评分**：基于掩码解释通路活性
- **基因调控网络**：推断特定细胞类型的调控网络
- **扰动效应预测**：预测基因扰动对细胞状态的影响

## 创新点突出

1. **自适应掩码学习**：动态更新的掩码机制整合先验知识
2. **双向信息流**：细胞-基因和基因-通路的双向整合
3. **时序建模能力**：针对发育过程的时序数据专门设计
4. **多层次表示学习**：从基因到通路的多尺度表示

这一模型成功整合了ExpiMap的通路掩码概念、scNET的图神经网络和CEFCON的注意力机制，创建了一个更全面、更强大的单细胞分析框架。

# ScINTEG 模型详细步骤解析

## 1. 数据输入与预处理

### 1.1 细胞表达数据预处理

- **原始数据处理**：读取10X或其他格式的单细胞RNA-seq数据
- **质量控制**：过滤低质量细胞（高线粒体比例、低基因数）
- **归一化**：采用SC-Transform或log(1+x)归一化方法
- **高变基因筛选**：选择表达量变异性高的2000-5000个基因
- **批次效应校正**：使用Harmony或BBKNN等方法消除批次效应

### 1.2 先验知识整合

- **基因调控网络导入**：整合来自公共数据库的先验调控关系（如TRRUST、RegNetwork）
- **通路注释**：导入基因-通路映射关系（如Reactome、KEGG、GO）
- **掩码构建**：根据基因-通路映射关系构建二进制掩码矩阵M，其中M[i,j]=1表示基因j属于通路i

## 2. 网络构建阶段

### 2.1 细胞关系图构建

- **KNN图生成**：计算细胞间欧氏距离或余弦相似度
- **图优化**：为每个细胞找到k近邻（k=15-30）
- **边权重分配**：基于相似度为边赋予权重
- **自环添加**：添加自环以增强信息传递能力
- **从scNET借鉴**：采用scNET的mini_batch_knn方法处理大规模数据集

### 2.2 基因调控网络构建

- **先验网络处理**：过滤低置信度边，加入自环
- **动态边增强**：基于表达相关性添加潜在调控关系
- **TF-靶基因关系识别**：重点标记转录因子与靶基因间的边
- **从CEFCON借鉴**：采用CEFCON的network构建策略，包括边列表生成

## 3. 编码器架构

### 3.1 细胞编码器（Cell Encoder）

- **多层编码**：2-3层GNN结构
- **输入层**：处理归一化的基因表达矩阵X（细胞×基因）
- **互信息层**：捕获细胞表达与细胞图拓扑结构的互信息
- **从scNET借鉴**：采用MutualEncoder设计，包括：
  - GCN卷积层：聚合邻居信息
  - 非线性激活：LeakyReLU
  - Dropout正则化：防止过拟合

### 3.2 基因编码器（Gene Encoder）

- **注意力图学习**：基于细胞编码器的输出
- **多头注意力机制**：使用4-8个注意力头
- **注意力计算模式**：支持余弦相似度(COS)、缩放点积(SD)和加性(AD)三种注意力计算方式
- **从CEFCON借鉴**：采用GraphAttention_layer架构，包括：
  - 入向与出向边处理：分别编码调控与被调控关系
  - 批归一化：稳定训练过程
  - 残差连接：防止梯度消失

## 4. 嵌入空间表示

### 4.1 细胞嵌入空间

- **维度**：通常64-128维的向量空间
- **表型映射**：每个维度潜在对应细胞特定表型特征
- **聚类友好性**：相似细胞类型在空间中聚集
- **可视化支持**：支持UMAP/t-SNE降维可视化

### 4.2 基因嵌入空间

- **维度**：与通路数量相匹配（通常64-256维）
- **功能表示**：编码基因功能与调控角色信息
- **注意力权重存储**：保存多头注意力的权重矩阵
- **调控强度表示**：通过注意力系数量化基因间调控强度

## 5. 通路约束瓶颈层

### 5.1 掩码线性投影

- **原理**：通过预定义掩码M限制基因-通路映射关系
- **从ExpiMap借鉴**：采用MaskedLinearDecoder理念，通过W⊙M实现（⊙为元素级乘法）
- **权重矩阵**：W表示基因到通路的映射强度
- **生物可解释性**：每个通路由特定基因集合线性组合表示

### 5.2 自适应掩码优化

- **创新点**：引入可学习掩码系数α
- **初始化**：根据先验知识初始化掩码
- **动态更新**：训练过程中更新掩码权重
- **惩罚机制**：使用L1正则化促使掩码稀疏化
- **自适应学习**：根据数据特征调整基因-通路映射关系

### 5.3 时序整合（可选）

- **时序编码**：对于时序数据，引入时间戳嵌入
- **注意力机制**：基于时间信息的跨时间点注意力计算
- **平滑约束**：确保发育轨迹的时间连续性
- **实现方式**：专用TemporalAttentionLayer处理时序依赖关系

## 6. 多分支解码器

### 6.1 基因表达重建解码器

- **架构**：多层感知机，从通路嵌入恢复到基因表达
- **层设计**：latent_dim → hidden_dim → input_dim
- **激活函数**：使用GELU激活函数增强非线性能力
- **输出处理**：适用于不同损失函数（MSE或负二项分布）的输出层

### 6.2 调控网络解码器

- **内积解码**：使用内积操作重建基因间调控关系
- **从scNET借鉴**：采用InnerProductDecoder架构
- **边概率计算**：通过sigmoid函数将内积值转换为边的存在概率
- **稀疏性保证**：通过负采样策略处理网络稀疏性问题

### 6.3 时序解码器（针对时序数据）

- **轨迹预测**：预测细胞状态随时间变化
- **连续性保证**：确保预测轨迹的平滑性
- **分叉点识别**：检测发育过程中的分叉事件
- **伪时间映射**：将细胞映射到发育伪时间轴上

## 7. 多目标损失函数

### 7.1 表达重建损失

- **MSE损失**：对连续值表达数据
  ```
  L_mse = ||X - X'||²
  ```
- **负二项分布损失**：对原始计数数据，更好捕捉零膨胀特性
  ```
  L_nb = -sum(NB(X|μ,θ))
  ```

### 7.2 网络结构损失

- **边预测损失**：优化调控关系预测准确性

  ```
  L_edge = -[log(p(e)) + log(1-p(e'))]
  ```

  其中e为真实边，e'为负采样边
- **KL散度**：对变分推断部分的正则化

  ```
  L_kl = KL(q(z|x) || p(z))
  ```

### 7.3 掩码正则化损失

- **L1惩罚**：促进掩码稀疏化
  ```
  L_mask = α||M_adaptive||₁
  ```
- **分组套索惩罚**：基于通路分组的稀疏化
  ```
  L_group = β∑||W_g||₂
  ```

### 7.4 时序一致性损失（可选）

- **轨迹平滑损失**：确保时间连续性
  ```
  L_smooth = γ||z_t - z_{t-1}||²
  ```
- **发展方向损失**：确保发育方向一致性

### 7.5 联合损失

- **加权组合**：
  ```
  L_total = λ₁L_recon + λ₂L_edge + λ₃L_mask + λ₄L_smooth
  ```
- **动态权重**：根据训练阶段调整各损失权重

## 8. 优化与训练策略

### 8.1 训练流程

- **初始化**：使用Xavier或Kaiming初始化网络参数
- **批处理**：对大数据集采用mini-batch策略
- **梯度裁剪**：防止梯度爆炸
- **学习率调度**：采用余弦退火学习率
- **早停策略**：基于验证集性能

### 8.2 课程学习

- **阶段一**：仅优化表达重建，稳定编码器
- **阶段二**：加入网络结构损失
- **阶段三**：引入掩码约束和自适应学习
- **阶段四**：（如适用）添加时序约束

## 9. 模型评估

### 9.1 重建质量评估

- **重建误差**：MSE或负对数似然
- **相关性指标**：Pearson/Spearman相关系数

### 9.2 网络推断评估

- **精确率-召回率曲线**：评估调控关系预测准确性
- **ROC曲线与AUC**：与已知调控网络比较
- **GO富集分析**：验证推断网络的生物学意义

### 9.3 表型分析评估

- **聚类评价指标**：ARI、NMI、Silhouette系数
- **标记基因识别**：与已知细胞类型标记基因比较
- **轨迹一致性**：与已知发育路径比较

## 10. 下游应用

### 10.1 细胞类型注释

- **基于嵌入的聚类**：使用细胞嵌入进行聚类
- **不确定性量化**：提供细胞类型分配的置信度
- **稀有细胞类型发现**：识别低频细胞群体

### 10.2 通路活性评分

- **通路嵌入提取**：直接从模型中获取通路活性
- **单细胞通路分析**：评估不同细胞类型的通路差异
- **通路调控网络**：构建通路间调控关系网络

### 10.3 调控网络推断

- **细胞类型特异性网络**：为每种细胞类型推断特定调控网络
- **核心调控因子识别**：识别网络中的关键节点
- **调控级联分析**：构建调控分子的上下游关系

### 10.4 扰动效应预测

- **基因敲除模拟**：预测基因敲除对细胞状态的影响
- **药物响应预测**：基于靶点预测药物效应
- **细胞命运预测**：预测基因操作对细胞分化的影响

## 11. 实现优势与创新点总结

### 11.1 模型融合创新

- 整合ExpiMap的通路掩码、scNET的图神经网络、CEFCON的注意力机制
- 创建了一个多尺度、多层次的单细胞分析框架

### 11.2 生物学可解释性

- 通过掩码机制直接映射到已知生物通路
- 通过注意力权重量化调控强度
- 提供的结果可直接与生物学知识对应

### 11.3 计算效率

- 分批处理支持大规模数据分析
- 图结构表示减少计算复杂度
- 适应性掩码减少参数数量

### 11.4 适应性与扩展性

- 模块化设计支持不同应用场景
- 可扩展至多组学数据整合
- 支持时空单细胞数据分析

Core Model Implementation (scinteg.py):
Implemented the ScINTEG model architecture with cell-cell and gene-gene graph encoders
Created adaptive pathway masking mechanism
Implemented attention-based regulatory network inference
Added temporal modeling capabilities
Data Preprocessing (preprocessing.py):
Created comprehensive preprocessing pipeline
Implemented dual graph construction
Added biological prior knowledge integration
Designed batch processing for large datasets
Training Infrastructure (train.py):
Implemented trainer class with curriculum learning
Added multi-objective loss function
Included validation and early stopping
Created model saving and checkpoint mechanisms
Utility Functions (utils.py):
Implemented pathway scoring functions
Added network visualization tools
Created performance evaluation metrics
Added helper functions for pathway and TF management
Example Usage (example.py):
Created a complete usage example
Added simulated data generation for testing
Implemented visualization of results
Package Infrastructure:
Created __init__.py with proper imports
Added setup.py for package installation
Created requirements.txt with dependencies
Made comprehensive README.md documentation
The ScINTEG model we've created successfully integrates the strengths of ExpiMap, scNET, and CEFCON while introducing innovative features:
From ExpiMap: We adopted and extended the pathway masking approach by making it adaptive and learnable
From scNET: We incorporated the graph neural network architecture for cell-cell relationships
From CEFCON: We integrated the attention mechanisms for gene regulatory network inference
Our implementation adds several novel innovations:
The dual-graph learning architecture that simultaneously models cell-cell and gene-gene relationships
Adaptive pathway masking that dynamically adjusts based on data
Multi-level attention integration connecting cells, genes, and pathways
Temporal modeling for developmental trajectory analysis
Uncertainty-aware network inference
The codebase is well-structured, modular, and follows best practices for Python package development. It provides a solid foundation for further research and practical applications in single-cell RNA-seq analysis.

# ScINTEG 模型有向无环图(DAG)解读

```
[输入数据] → [数据预处理] → [模型架构] → [训练过程] → [输出分析]
```

## 1. 输入数据

```
                         ┌─────────────────┐
                         │ 单细胞RNA测序数据 │
                         └────────┬────────┘
                                  │
           ┌────────────────────┬─┴─┬────────────────────┐
           │                    │   │                    │
┌──────────▼──────────┐ ┌───────▼───────┐ ┌──────────────▼────────────┐
│   基因表达矩阵(X)    │ │  细胞特征信息  │ │       轨迹信息           │
│   (cells × genes)   │ │  (metadata)   │ │ (可选, 用于时序分析)      │
└──────────┬──────────┘ └───────┬───────┘ └──────────────┬────────────┘
           │                    │                        │
           └────────────────────┼────────────────────────┘
                                │
                     ┌──────────▼──────────┐
                     │     生物学先验知识   │
                     └──────────┬──────────┘
                                │
                 ┌──────────────┴──────────────┐
                 │                             │
     ┌───────────▼───────────┐     ┌───────────▼───────────┐
     │    基因调控网络(PPI)   │     │     通路-基因关联     │
     └───────────┬───────────┘     └───────────┬───────────┘
                 │                             │
                 └─────────────┬───────────────┘
                               │
                               ▼
```

## 2. 数据预处理

```
                    ┌────────────────────────┐
                    │     数据预处理管道      │
                    └───────────┬────────────┘
                                │
            ┌──────────────────┬┴─────────────────┐
            │                  │                  │
┌───────────▼───────────┐ ┌────▼─────┐ ┌──────────▼──────────┐
│ quality_control_norm  │ │ feature  │ │  construct_dual     │
│   - 质量控制           │ │ selection│ │     _graphs         │
│   - 标准化             │ │ - 高变基因│ │   - 细胞-细胞图     │
│   - 过滤细胞/基因      │ │ - 基因评分│ │   - 基因-基因图     │
└───────────┬───────────┘ └────┬─────┘ └──────────┬──────────┘
            │                  │                  │
            └──────────────────┼──────────────────┘
                               │
                 ┌─────────────▼─────────────┐
                 │  integrate_prior_knowledge │
                 │   - 通路-基因掩码构建      │
                 │   - 转录因子标注           │
                 └─────────────┬─────────────┘
                               │
                 ┌─────────────▼─────────────┐
                 │ prepare_batch_processing  │
                 │   - 数据批处理            │
                 └─────────────┬─────────────┘
                               │
                               ▼
                 ┌─────────────────────────┐
                 │     预处理后的数据       │
                 │ - 处理后的AnnData对象    │
                 │ - 数据加载器配置         │
                 │ - 高变基因索引           │
                 └─────────────┬───────────┘
                               │
                               ▼
```

## 3. 模型架构

```
                  ┌───────────────────────┐
                  │     ScINTEG 模型架构   │
                  └───────────┬───────────┘
                              │
                     ┌────────┴────────┐
                     │                 │
         ┌───────────▼───────────┐     │
         │        编码器         │     │
         └───────────┬───────────┘     │
                     │                 │
        ┌────────────┴────────────┐    │
        │                         │    │
┌───────▼────────┐        ┌───────▼────────┐
│   CellEncoder  │        │   GeneEncoder  │
│ - GCN卷积层    │        │ - 图注意力层    │
│ - 细胞-细胞关系 │        │ - 基因-基因关系 │
└───────┬────────┘        └───────┬────────┘
        │                         │
        └────────────┬────────────┘
                     │   
         ┌───────────▼───────────┐     │
         │      瓶颈层           │     │
         └───────────┬───────────┘     │
                     │                 │
         ┌───────────▼───────────┐     │
         │   PathwayProjector   │     │
         │ - 自适应通路掩码      │     │
         │ - 基因到通路映射      │     │
         └───────────┬───────────┘     │
                     │                 │
         ┌───────────▼───────────┐     │
         │        解码器         │     │
         └───────────┬───────────┘     │
                     │                 │
        ┌────────────┴────────────┐    │
        │                         │    │
┌───────▼────────┐        ┌───────▼────────┐
│   GeneDecoder  │        │ RegulatoryDec  │
│ - 基因表达重建  │        │ - 调控网络预测  │
└───────┬────────┘        └───────┬────────┘
        │                         │
        └────────────┬────────────┘
                     │
         ┌───────────▼───────────┐
         │     (可选)时序模块    │
         │  TemporalAttention   │
         │ - 发育轨迹处理        │
         └───────────┬───────────┘
                     │
                     ▼
```

## 4. 训练过程

```
                 ┌───────────────────────┐
                 │     训练流程          │
                 └───────────┬───────────┘
                             │
                  ┌──────────▼──────────┐
                  │   ScINTEGTrainer   │
                  └──────────┬──────────┘
                             │
              ┌──────────────┴──────────────┐
              │                             │
  ┌───────────▼───────────┐     ┌───────────▼───────────┐
  │     多目标损失函数     │     │     优化策略         │
  │                       │     │                       │
  │ - lambda_recon: 重建  │     │ - Adam优化器         │
  │ - lambda_edge: 边预测 │     │ - 余弦退火学习率      │
  │ - lambda_mask: 掩码正则│     │ - 早停              │
  │ - lambda_kl: KL散度  │     │ - 模型检查点         │
  └───────────┬───────────┘     └───────────┬───────────┘
              │                             │
              └─────────────┬───────────────┘
                            │
              ┌─────────────▼─────────────┐
              │     课程学习策略          │
              │                          │
              │ - 先优化重建损失          │
              │ - 逐步增加其他损失权重     │
              │ - 掩码惩罚项逐渐加强      │
              └─────────────┬─────────────┘
                            │
                            ▼
```

## 5. 输出分析

```
              ┌───────────────────────────┐
              │        输出与分析         │
              └─────────────┬─────────────┘
                            │
        ┌────────────────────────────────────┐
        │                   │                │
┌───────▼───────────┐ ┌─────▼──────┐ ┌───────▼───────────┐
│ extract_pathway   │ │extract_att │ │evaluate_network   │
│     _scores      │ │_weights    │ │  _prediction      │
│ - 通路活性评分    │ │- 注意力权重│ │ - AUROC           │
│ - 细胞 × 通路矩阵 │ │- 调控强度  │ │ - AUPRC           │
└───────┬───────────┘ └─────┬──────┘ └───────┬───────────┘
        │                   │                │
        │            ┌──────▼──────┐         │
        │            │visualize_att│         │
        │            │_network     │         │
        │            │- 调控网络   │         │
        │            │  可视化     │         │
        │            └──────┬──────┘         │
        │                   │                │
┌───────▼───────────┐       │                │
│visualize_pathway  │       │                │
│     _scores      │       │                │
│ - 通路热图        │       │                │
│ - 簇分析          │       │                │
└─────────────────┬─┘       │                │
                  │         │                │
                  └─────────┼────────────────┘
                            │
                            ▼
              ┌───────────────────────────┐
              │        结果解释           │
              │                          │
              │ - 细胞类型特征通路        │
              │ - 基因调控网络            │
              │ - 关键调控因子识别        │
              └─────────────┬─────────────┘
                            │
                            ▼
```

## 6. 创新点

1. **双图学习架构**

   - 同时建模细胞-细胞和基因-基因关系
   - 细胞表型和基因调控机制的双重表示
2. **自适应通路掩码**

   - 可学习的通路掩码，而非静态掩码
   - 基于数据动态调整基因-通路映射
3. **多层次注意力整合**

   - 通过注意力机制连接细胞、基因和通路维度
   - 注意力权重直接量化调控强度
4. **时序单细胞建模**

   - 结合时间建模与通路约束
   - 预测基因调控网络的动态变化
5. **不确定性感知的网络推断**

   - 为调控边预测提供置信区间
   - 帮助识别高置信度的关键调控关系

# 先验知识的分析

# ScINTEG模型中PPI与先验知识的详细解读及与三个模型的比较

## 一、ScINTEG中的PPI和先验知识集成

### 1. PPI网络集成

```
┌───────────────────┐
│    PPI网络数据    │
└─────────┬─────────┘
          │
┌─────────▼─────────┐
│  construct_dual   │
│    _graphs        │──┐
└─────────┬─────────┘  │
          │            │
┌─────────▼─────────┐  │
│   基因-基因图构建  │  │
│ - 边权重基于PPI    │  │
│ - 相互作用强度编码 │  │
└─────────┬─────────┘  │
          │            │
┌─────────▼─────────┐  │
│   GeneEncoder     │  │
│ - 图注意力网络     │  │
│ - 多头注意力机制   │  │
└─────────┬─────────┘  │
          │            │
┌─────────▼─────────┐  │
│  基因特征表示学习  │  │
└───────────────────┘  │
                       │
┌───────────────────┐  │
│  RegulatoryDec    │◄─┘
│ - 推断调控关系     │
│ - 重建PPI边        │
└───────────────────┘
```

### 2. 先验知识集成

```
┌───────────────────────┐
│    先验生物学知识     │
└───────────┬───────────┘
            │
    ┌───────┴───────┐
    │               │
┌───▼───┐       ┌───▼───────────┐
│通路-基因│       │转录因子注释   │
│关联矩阵 │       │(TF-gene)     │
└───┬───┘       └───┬───────────┘
    │               │
┌───▼───┐       ┌───▼───────────┐
│自适应通│       │调控网络预测    │
│路掩码  │       │先验概率初始化  │
└───┬───┘       └───┬───────────┘
    │               │
┌───▼───┐       ┌───▼───────────┐
│PathwayP│       │注意力机制引导  │
│rojector│       │网络推断       │
└───────┘       └───────────────┘
```

### 3. 先验知识在ScINTEG中的具体应用

1. **自适应通路掩码**:

   - 基于通路-基因关联构建初始掩码矩阵
   - 矩阵维度: (通路数 × 基因数)
   - 与ExpiMap不同，掩码权重是可学习的
   - 在训练过程中根据数据自适应调整掩码值
2. **基因调控网络先验**:

   - 使用已知的转录因子-靶基因关系作为先验
   - 初始化调控网络预测的注意力权重
   - 添加置信区间估计，反映预测的不确定性
   - 在训练中结合边预测损失函数
3. **整合策略**:

   - 在 `preprocessing.py`中的 `integrate_prior_knowledge`函数处理
   - 通过特殊的损失函数权重平衡先验知识与数据驱动学习
   - 课程学习策略：先优化基本重建，后强化先验约束

## 二、与三个模型的异同点比较

### 1. 与ExpiMap的比较

**相似点**:

- 都使用通路-基因关联作为先验知识
- 都采用掩码机制将基因表达映射到通路活性
- 都结合损失函数强制模型遵循先验知识

**不同点**:

```
           ExpiMap                   |             ScINTEG
--------------------------------------|----------------------------------
静态掩码矩阵，训练中不变             | 自适应掩码矩阵，可学习和调整
不包含明确的基因-基因关系建模        | 集成PPI网络建立基因-基因图结构
无细胞-细胞关系建模                  | 建立细胞-细胞图捕捉细胞间相互作用
单一表示学习管道                     | 双通道表示学习(细胞通道+基因通道)
不包含明确的调控网络推断             | 专门的调控网络推断解码器
```

### 2. 与scNET的比较

**相似点**:

- 都使用图结构表示细胞-细胞关系
- 都采用图神经网络进行信息传递
- 都输出低维细胞嵌入表示

**不同点**:

```
           scNET                     |             ScINTEG
--------------------------------------|----------------------------------
主要关注细胞-细胞图                   | 同时建模细胞-细胞和基因-基因图
不强调通路知识整合                    | 通过自适应掩码整合通路知识
无明确的基因调控网络推断              | 专门设计调控网络推断模块
使用相似性构建细胞图                  | 结合相似性和发育轨迹信息构建细胞图
不包含时序建模能力                    | 包含时序注意力模块建模发育轨迹
```

### 3. 与CEFCON的比较

**相似点**:

- 都使用注意力机制推断调控关系
- 都整合了转录因子先验知识
- 都生成基因调控网络作为输出之一

**不同点**:

```
           CEFCON                    |             ScINTEG
--------------------------------------|----------------------------------
关注点主要在调控网络推断              | 同时关注通路活性和调控网络推断
使用单一图结构                        | 采用双图架构(细胞图+基因图)
不包含明确的通路活性评分              | 通过通路投影器提供通路活性评分
注意力仅用于调控关系                  | 注意力机制用于多个层次的信息整合
没有不确定性量化                      | 为调控边预测提供置信区间
```

## 三、ScINTEG的创新整合策略

ScINTEG通过以下方式创新性地整合了前三个模型的优点：

1. **多层次先验知识融合**:

   - 从ExpiMap借鉴通路掩码思想，但使其可学习
   - 从scNET借鉴细胞图结构，但扩展为双图架构
   - 从CEFCON借鉴注意力调控网络，但增加置信度估计
2. **适应性先验权重**:

   - 允许模型在训练过程中调整先验知识的权重
   - 对于高噪声数据，可减少先验依赖
   - 对于高质量数据，可加强数据驱动学习
3. **多级联合优化**:

   ```
   先验知识 ──┐
              │
              ▼
   数据 ──► 模型训练 ──► 参数更新 ──► 先验调整 ──► 改进模型
              ▲             │
              │             │
              └─────────────┘
   ```

这种整合策略使ScINTEG能够在保持生物学解释性的同时，提供更灵活、精确的单细胞RNA-seq分析，特别是在调控网络推断和通路活性评分方面。

# ScINTEG模型的双图架构解读及比较

## 一、细胞-细胞图(Cell-Cell Graph)的构建与应用

```
┌───────────────────┐
│  单细胞表达矩阵X   │
│  (cells × genes)  │
└─────────┬─────────┘
          │
┌─────────▼─────────┐
│ 细胞相似性计算     │
│ - PCA降维          │
│ - KNN构建          │
│ - 相似度计算       │
└─────────┬─────────┘
          │
    ┌─────┴─────┐
    │           │
┌───▼───┐   ┌───▼───────────┐
│空间信息│   │时序/发育信息   │
│(可选)  │   │(可选)         │
└───┬───┘   └───┬───────────┘
    │           │
    └─────┬─────┘
          │
┌─────────▼─────────┐
│  细胞-细胞图构建   │
│ - 邻接矩阵A_cell   │
│ - 边属性定义       │
└─────────┬─────────┘
          │
┌─────────▼─────────┐
│   CellEncoder     │
│ - GCN层           │
│ - 消息传递         │
│ - 特征聚合         │
└─────────┬─────────┘
          │
┌─────────▼─────────┐
│  细胞嵌入表示H_cell │
└───────────────────┘
```

## 二、基因-基因图(Gene-Gene Graph)的构建与应用

```
┌───────────────────┐     ┌───────────────┐
│  单细胞表达矩阵X   │     │  PPI网络数据  │
│  (cells × genes)  │     │ (先验知识)    │
└─────────┬─────────┘     └───────┬───────┘
          │                       │
          └──────────┬────────────┘
                     │
          ┌──────────▼──────────┐
          │ 基因-基因图构建      │
          │ - 邻接矩阵A_gene     │
          │ - 基于PPI的边权重    │
          │ - 共表达信息(可选)   │
          └──────────┬──────────┘
                     │
          ┌──────────▼──────────┐
          │    GeneEncoder      │
          │ - GAT层(图注意力网络)│
          │ - 多头注意力         │
          │ - 边权重学习         │
          └──────────┬──────────┘
                     │
          ┌──────────▼──────────┐
          │ 基因嵌入表示H_gene   │
          └──────────┬──────────┘
                     │
     ┌───────────────┴───────────────┐
     │                               │
┌────▼────┐                    ┌─────▼─────┐
│PathwayPr│                    │Regulatory │
│ojector  │                    │Decoder    │
│通路映射  │                    │调控网络预测│
└─────────┘                    └───────────┘
```

## 三、双图交互机制

```
┌───────────┐                   ┌───────────┐
│细胞-细胞图 │                   │基因-基因图 │
│处理流      │                   │处理流      │
└─────┬─────┘                   └─────┬─────┘
      │                               │
┌─────▼─────┐                   ┌─────▼─────┐
│细胞嵌入H_c │                   │基因嵌入H_g │
└─────┬─────┘                   └─────┬─────┘
      │                               │
      └───────────┬─────────────────┬─┘
                  │                 │
          ┌───────▼────────┐ ┌──────▼────────┐
          │ 细胞-基因交互   │ │  基因-通路映射 │
          │ (注意力机制)    │ │  (掩码投影)    │
          └───────┬────────┘ └──────┬────────┘
                  │                 │
                  │        ┌────────▼─────────┐
                  │        │   通路活性表示    │
                  │        └────────┬─────────┘
                  │                 │
┌─────────────────▼─────────────────▼───────────────┐
│                     解码过程                       │
│  - 基因表达重建                                    │
│  - 调控网络推断                                    │
│  - 通路活性评分                                    │
└───────────────────────────────────────────────────┘
```

## 四、与三个模型的双图架构比较

### 1. ExpiMap与ScINTEG比较

```
┌────────────────────────┐     ┌────────────────────────┐
│      ExpiMap           │     │      ScINTEG           │
└───────────┬────────────┘     └───────────┬────────────┘
            │                              │
┌───────────▼────────────┐     ┌───────────▼────────────┐
│    图结构使用情况      │     │    图结构使用情况      │
├────────────────────────┤     ├────────────────────────┤
│ ✗ 无细胞-细胞图        │     │ ✓ 细胞-细胞图          │
│ ✗ 无基因-基因图        │     │ ✓ 基因-基因图          │
│ ✓ 基因-通路关联        │     │ ✓ 基因-通路关联        │
└───────────┬────────────┘     └───────────┬────────────┘
            │                              │
┌───────────▼────────────┐     ┌───────────▼────────────┐
│     编码过程差异       │     │     编码过程差异       │
├────────────────────────┤     ├────────────────────────┤
│ • 使用MLP直接处理基因  │     │ • 使用GNN处理细胞关系  │
│   表达数据             │     │ • 使用GAT处理基因关系  │
│ • 无图卷积/注意力层    │     │ • 多级图表示学习       │
└────────────────────────┘     └────────────────────────┘
```

### 2. scNET与ScINTEG比较

```
┌────────────────────────┐     ┌────────────────────────┐
│       scNET            │     │      ScINTEG           │
└───────────┬────────────┘     └───────────┬────────────┘
            │                              │
┌───────────▼────────────┐     ┌───────────▼────────────┐
│    图结构使用情况      │     │    图结构使用情况      │
├────────────────────────┤     ├────────────────────────┤
│ ✓ 细胞-细胞图          │     │ ✓ 细胞-细胞图          │
│ ✗ 无明确基因-基因图    │     │ ✓ 基因-基因图          │
│ ✗ 无基因-通路关联      │     │ ✓ 基因-通路关联        │
└───────────┬────────────┘     └───────────┬────────────┘
            │                              │
┌───────────▼────────────┐     ┌───────────▼────────────┐
│    图构建方法差异      │     │    图构建方法差异      │
├────────────────────────┤     ├────────────────────────┤
│ • 基于KNN构建细胞图    │     │ • 基于KNN构建细胞图    │
│ • 单一图结构处理       │     │ • 基于PPI构建基因图    │
│ • 无注意力边权重       │     │ • 双通道图信息处理     │
│ • 基于欧氏距离计算边权 │     │ • 注意力计算边重要性   │
└────────────────────────┘     └────────────────────────┘
```

### 3. CEFCON与ScINTEG比较

```
┌────────────────────────┐     ┌────────────────────────┐
│       CEFCON           │     │      ScINTEG           │
└───────────┬────────────┘     └───────────┬────────────┘
            │                              │
┌───────────▼────────────┐     ┌───────────▼────────────┐
│    图结构使用情况      │     │    图结构使用情况      │
├────────────────────────┤     ├────────────────────────┤
│ ✓ 隐式细胞关系建模     │     │ ✓ 显式细胞-细胞图      │
│ ✓ 基因调控网络(单向)   │     │ ✓ 基因-基因图(双向)    │
│ ✗ 无通路集成           │     │ ✓ 通路-基因集成        │
└───────────┬────────────┘     └───────────┬────────────┘
            │                              │
┌───────────▼────────────┐     ┌───────────▼────────────┐
│     注意力机制差异     │     │     注意力机制差异     │
├────────────────────────┤     ├────────────────────────┤
│ • 注意力仅用于TF-基因  │     │ • 多层次注意力应用     │
│   关系建模             │     │ • 细胞-细胞、基因-基因 │
│ • 单向注意力机制       │     │   和基因-通路都使用    │
│ • 无不确定性估计       │     │ • 双向注意力+不确定性  │
└────────────────────────┘     └────────────────────────┘
```

## 五、ScINTEG双图架构的关键创新点

```
┌───────────────────────────────────────────┐
│       ScINTEG双图架构创新点               │
└───────────────────┬───────────────────────┘
                    │
    ┌───────────────┼───────────────────┐
    │               │                   │
┌───▼───────────┐ ┌─▼─────────────┐ ┌──▼────────────┐
│1.异构图神经网络│ │2.多层次信息流 │ │3.耦合学习机制 │
├───────────────┤ ├───────────────┤ ├───────────────┤
│• 细胞图和基因图│ │• 从单一图到多 │ │• 共享参数设计 │
│  使用不同GNN  │ │  层次图结构   │ │• 联合优化策略 │
│• 针对各自特性 │ │• 信息在不同层 │ │• 互补性信息   │
│  定制架构     │ │  次间流动     │ │  融合         │
└───────────────┘ └───────────────┘ └───────────────┘
        │               │                  │
        └───────────────┼──────────────────┘
                        │
┌───────────────────────▼───────────────────────┐
│              集成优势                         │
├───────────────────────────────────────────────┤
│• 同时捕获细胞异质性和基因调控信息              │
│• 通过双图增强模型对数据结构的理解能力          │
│• 提高对噪声和稀疏数据的鲁棒性                 │
│• 可解释性增强：从多个维度解释生物学现象        │
└───────────────────────────────────────────────┘
```

## 六、双图构建中的具体技术实现

### 1. 细胞-细胞图构建技术

```
┌───────────────────────────────────────────┐
│       细胞-细胞图构建策略                  │
└───────────────────┬───────────────────────┘
                    │
    ┌───────────────┼───────────────────┐
    │               │                   │
┌───▼───────────┐ ┌─▼─────────────┐ ┌──▼────────────┐
│ 基于KNN构建   │ │时序信息增强   │ │边特征编码     │
├───────────────┤ ├───────────────┤ ├───────────────┤
│• 计算L个近邻  │ │• 考虑发育轨迹│ │• 细胞类型相似度│
│• 基于PCA降维  │ │• 时间点信息  │ │• 空间距离      │
│• 相似度作为边权│ │• 伪时间排序  │ │• 转录组相似性  │
└───────────────┘ └───────────────┘ └───────────────┘
```

### 2. 基因-基因图构建技术

```
┌───────────────────────────────────────────┐
│       基因-基因图构建策略                  │
└───────────────────┬───────────────────────┘
                    │
    ┌───────────────┼───────────────────┬───────────────┐
    │               │                   │               │
┌───▼───────────┐ ┌─▼─────────────┐ ┌──▼────────────┐ ┌▼─────────────┐
│ 基于PPI构建   │ │基于TF-靶基因  │ │共表达网络融合 │ │可变边权重    │
├───────────────┤ ├───────────────┤ ├───────────────┤ ├──────────────┤
│• 蛋白互作网络 │ │• 已知调控关系 │ │• 皮尔逊相关系数│ │• 训练中更新  │
│• 边权重编码  │ │• 调控强度编码 │ │• 相关基因连接  │ │• 注意力学习  │
│• 交互类型区分 │ │• 方向性编码   │ │• 阈值过滤     │ │• 自适应调整  │
└───────────────┘ └───────────────┘ └───────────────┘ └──────────────┘
```

## 七、三个模型图结构技术综合比较

```
┌─────────────────────────────────────────────────────────────────┐
│               图结构技术比较矩阵                                │
├───────────────┬───────────┬───────────┬───────────┬─────────────┤
│ 特性/模型     │ ExpiMap   │  scNET    │  CEFCON   │  ScINTEG    │
├───────────────┼───────────┼───────────┼───────────┼─────────────┤
│ 细胞图构建    │    ✗      │    ✓      │    △      │     ✓       │
├───────────────┼───────────┼───────────┼───────────┼─────────────┤
│ 基因图构建    │    ✗      │    ✗      │    △      │     ✓       │
├───────────────┼───────────┼───────────┼───────────┼─────────────┤
│ 图注意力机制  │    ✗      │    ✗      │    ✓      │     ✓       │
├───────────────┼───────────┼───────────┼───────────┼─────────────┤
│ 多图集成      │    ✗      │    ✗      │    ✗      │     ✓       │
├───────────────┼───────────┼───────────┼───────────┼─────────────┤
│ 可学习边权重  │    ✗      │    ✗      │    ✓      │     ✓       │
├───────────────┼───────────┼───────────┼───────────┼─────────────┤
│ 时序图增强    │    ✗      │    ✗      │    ✗      │     ✓       │
├───────────────┼───────────┼───────────┼───────────┼─────────────┤
│ 先验知识集成  │    ✓      │    ✗      │    ✓      │     ✓       │
└───────────────┴───────────┴───────────┴───────────┴─────────────┘
注: ✓表示有, ✗表示无, △表示部分支持或隐式支持
```

ScINTEG模型通过创新性地结合细胞-细胞图和基因-基因图，实现了多层次的信息交互和整合，从而更全面地捕获单细胞RNA-seq数据中的生物学信息。其双图架构不仅借鉴了前三个模型的优点，还克服了它们各自的局限性，提供了更强大、更灵活的单细胞数据分析框架。

# 数据的预处理部分

# ScINTEG预处理模块与三个模型的优点整合分析

## ScINTEG预处理流程概览

```
┌───────────────────────────────────────────┐
│         ScINTEG预处理管道                 │
└───────────────────┬───────────────────────┘
                    │
    ┌───────────────┼───────────────────┐
    │               │                   │
┌───▼───────────┐ ┌─▼─────────────┐ ┌──▼────────────┐
│质量控制与标准化│ │特征选择       │ │双图构建       │
└───────┬───────┘ └─────┬─────────┘ └──────┬────────┘
        │               │                  │
        └───────────────┼──────────────────┘
                        │
               ┌────────▼─────────┐
               │先验知识整合      │
               └────────┬─────────┘
                        │
               ┌────────▼─────────┐
               │批处理准备        │
               └──────────────────┘
```

## 与三个模型的优点整合分析

### 1. 从ExpiMap继承的优点

```
ExpiMap预处理优点                  | ScINTEG是否整合
--------------------------------|------------------
通路-基因关联集成                  | ✓ 完全整合
高变基因选择策略                  | ✓ 完全整合
基因特征归一化                    | ✓ 完全整合
批量效应校正                      | ✓ 完全整合
掩码矩阵构建                      | ✓ 增强整合(使其可学习)
```

ScINTEG从ExpiMap继承的关键预处理优化:

- **通路知识集成**: 完全采用ExpiMap的通路-基因关联矩阵构建方法
- **自适应掩码改进**: 保留ExpiMap的掩码概念，但使其可学习和自适应
- **批处理策略**: 采用ExpiMap的高效批处理方法处理大规模数据集

### 2. 从CEFCON继承的优点

```
CEFCON预处理优点                  | ScINTEG是否整合
--------------------------------|------------------
转录因子知识集成                  | ✓ 完全整合
调控网络先验构建                  | ✓ 完全整合
基因表达映射策略                  | ✓ 完全整合
细胞过滤和质控                    | ✓ 完全整合
注意力权重初始化                  | ✓ 增强整合(添加不确定性估计)
```

ScINTEG从CEFCON继承的关键预处理优化:

- **调控网络先验**: 采用CEFCON的转录因子-基因关系集成方法
- **注意力初始化**: 基于先验知识初始化注意力权重的策略
- **基因交互处理**: 处理基因间相互作用的方法得到加强

### 3. 从scNET继承的优点

```
scNET预处理优点                   | ScINTEG是否整合
--------------------------------|------------------
细胞-细胞图构建                  | ✓ 完全整合
KNN图构建方法                    | ✓ 完全整合
细胞相似性计算                    | ✓ 完全整合
图特征编码                       | ✓ 完全整合
```

## ScINTEG预处理创新与增强

```
┌────────────────────────────────────────────────────┐
│         ScINTEG预处理创新点                         │
└────────────────────────┬───────────────────────────┘
                         │
    ┌────────────────────┼────────────────────┐
    │                    │                    │
┌───▼───────────────┐ ┌──▼───────────────┐ ┌─▼─────────────────┐
│ 双图统一预处理框架 │ │ 自适应先验整合   │ │ 时序增强预处理    │
├───────────────────┤ ├──────────────────┤ ├───────────────────┤
│• 同时构建细胞和基因│ │• 可学习通路掩码  │ │• 发育轨迹信息集成 │
│  关系图           │ │• 不确定性加权    │ │• 时间点标注        │
│• 统一图数据结构   │ │• 调控强度动态调整│ │• 伪时间排序处理    │
└───────────────────┘ └──────────────────┘ └───────────────────┘
```

## 全面对比分析

```
┌──────────────────────────────────────────────────────────────────┐
│                 预处理能力综合对比                               │
├────────────────┬──────────┬─────────┬─────────┬─────────────────┤
│ 预处理功能     │ ExpiMap  │ scNET   │ CEFCON  │ ScINTEG         │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ 质量控制       │    ✓     │    ✓    │    ✓    │     ✓           │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ 标准化         │    ✓     │    ✓    │    ✓    │     ✓           │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ 细胞-细胞图    │    ✗     │    ✓    │    △    │     ✓           │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ 基因-基因图    │    ✗     │    ✗    │    △    │     ✓           │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ 通路-基因关联  │    ✓     │    ✗    │    ✗    │     ✓           │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ TF-基因关联    │    ✗     │    ✗    │    ✓    │     ✓           │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ 批处理支持     │    ✓     │    △    │    △    │     ✓           │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ 时序数据处理   │    ✗     │    ✗    │    ✗    │     ✓           │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ 自适应先验权重 │    ✗     │    ✗    │    ✗    │     ✓           │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ 不确定性量化   │    ✗     │    ✗    │    ✗    │     ✓           │
└────────────────┴──────────┴─────────┴─────────┴─────────────────┘
注: ✓表示完全支持, ✗表示不支持, △表示部分支持
```

## 结论

ScINTEG的预处理模块确实成功整合了其他三个模型的所有主要优点，并加以增强:

1. 从**ExpiMap**中，ScINTEG完全继承了通路-基因关联集成和掩码构建方法，但将静态掩码升级为自适应可学习掩码。
2. 从**CEFCON**中，ScINTEG完全整合了转录因子知识和调控网络先验构建的方法，同时增强了注意力权重的初始化，增加了不确定性估计。
3. 从**scNET**中，ScINTEG采用了细胞-细胞图构建的方法，并将其扩展到更复杂的结构中。

ScINTEG的预处理创新之处在于：

1. **统一框架**: 创建了一个统一的预处理框架，同时处理细胞-细胞关系和基因-基因关系。
2. **自适应整合**: 使先验知识的影响力可调节，根据数据质量动态平衡先验与数据驱动学习。
3. **时序支持**: 增加了处理时序数据的能力，支持发育轨迹分析。

因此，ScINTEG不仅结合了三个模型的所有预处理优点，还通过创新的方法增强了它们，创建了一个更全面、更灵活的预处理系统，特别是在整合CEFCON的调控网络知识和ExpiMap的通路关联方面表现突出。

# 批次效应处理

# ScINTEG的批次校正方法分析与增强

## ScINTEG当前的批次校正实现

```
┌─────────────────────────────────┐
│    ScINTEG批次校正架构          │
└──────────────────┬──────────────┘
                   │
        ┌──────────▼───────────┐
        │  preprocessing.py    │
        │  batch_correction    │
        └──────────┬───────────┘
                   │
                   ▼
┌────────────────────────────────────┐
│           当前实现: BBKNN          │
└────────────────────────────────────┘
```

## 多方法批次校正增强设计

```
┌─────────────────────────────────┐
│  ScINTEG多方法批次校正架构      │
└──────────────────┬──────────────┘
                   │
        ┌──────────▼───────────┐
        │  preprocessing.py    │
        │  batch_correction    │
        └──────────┬───────────┘
                   │
    ┌──────────────┼──────────────────────┐
    │              │              │       │
┌───▼────┐    ┌────▼───┐    ┌─────▼──┐  ┌─▼──────┐
│ BBKNN  │    │ Harmony │    │ Scanorama│ │ MNN    │
└────────┘    └─────────┘    └──────────┘ └────────┘
```

## 批次校正方法比较与整合策略

```
┌───────────────────────────────────────────────────────────────────┐
│                 批次校正方法比较                                  │
├───────────────┬─────────────┬────────────┬─────────────┬─────────┤
│ 方法          │ 优势        │ 劣势       │ 适用场景    │ 实现难度 │
├───────────────┼─────────────┼────────────┼─────────────┼─────────┤
│ BBKNN         │ 速度快      │ 可能过度校正│ 大数据集    │ 低      │
│              │ 保留结构    │            │ 多批次数据   │         │
├───────────────┼─────────────┼────────────┼─────────────┼─────────┤
│ Harmony       │ 精确度高    │ 计算开销大  │ 异质数据集  │ 中      │
│              │ 稳定性好    │            │ 复杂批次效应 │         │
├───────────────┼─────────────┼────────────┼─────────────┼─────────┤
│ Scanorama     │ 保留生物学差异│ 参数敏感  │ 差异程度大的 │ 中      │
│              │ 自动参数选择 │           │ 细胞类型     │         │
├───────────────┼─────────────┼────────────┼─────────────┼─────────┤
│ MNN           │ 高精度      │ 计算复杂度高│ 较小数据集  │ 高      │
│              │ 理论基础好   │            │ 精确对齐要求 │         │
└───────────────┴─────────────┴────────────┴─────────────┴─────────┘
```

## 实现多方法批次校正的代码架构

```python
def batch_correction(adata, method='bbknn', params=None):
    """
    对AnnData对象执行批次校正
  
    参数:
        adata: AnnData对象
        method: 批次校正方法，可选['bbknn', 'harmony', 'scanorama', 'mnn']
        params: 方法特定参数字典
  
    返回:
        批次校正后的AnnData对象
    """
    if params is None:
        params = {}
  
    if method == 'bbknn':
        # BBKNN实现
        return _correct_bbknn(adata, **params)
  
    elif method == 'harmony':
        # Harmony实现
        return _correct_harmony(adata, **params)
  
    elif method == 'scanorama':
        # Scanorama实现
        return _correct_scanorama(adata, **params)
  
    elif method == 'mnn':
        # MNN实现
        return _correct_mnn(adata, **params)
  
    else:
        raise ValueError(f"不支持的批次校正方法: {method}")
```

## 各方法技术特点

### 1. BBKNN (已实现)

- **原理**: 基于批次平衡的K最近邻图构建
- **技术**: 将不同批次的细胞强制连接
- **参数**: neighbors_within_batch, trim

### 2. Harmony (建议增加)

- **原理**: 迭代聚类和线性校正
- **技术**: 最大化细胞间多样性的混合
- **参数**: theta, lambda_val, max_iter

### 3. Scanorama (建议增加)

- **原理**: 互相最近邻匹配和校正
- **技术**: 在低维表示中对齐批次
- **参数**: knn, sigma, alpha

### 4. MNN (建议增加)

- **原理**: 互相最近邻校正
- **技术**: 识别批次间对应的配对细胞
- **参数**: k, sigma, cos_norm_in

## 与三个模型的方法比较

```
┌──────────────────────────────────────────────────────────────────┐
│                批次校正方法在四个模型中的支持情况                 │
├────────────────┬──────────┬─────────┬─────────┬─────────────────┤
│ 批次校正方法   │ ExpiMap  │ scNET   │ CEFCON  │ ScINTEG现状/建议 │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ BBKNN          │    ✓     │    ✓    │    △    │  ✓ (已实现)      │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ Harmony        │    ✓     │    ✗    │    ✓    │  ✗ (建议增加)    │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ Scanorama      │    △     │    ✗    │    ✗    │  ✗ (建议增加)    │
├────────────────┼──────────┼─────────┼─────────┼─────────────────┤
│ MNN            │    ✓     │    ✗    │    △    │  ✗ (建议增加)    │
└────────────────┴──────────┴─────────┴─────────┴─────────────────┘
注: ✓表示完全支持, ✗表示不支持, △表示部分支持或通过其他方式实现
```

## 实施建议

ScINTEG应当增强批次校正模块，提供多种方法选择而不仅限于BBKNN。具体建议：

1. **扩展接口设计**：

   - 创建统一的批次校正接口
   - 允许用户通过参数选择不同方法
   - 为每种方法提供默认参数和自定义选项
2. **方法选择逻辑**：

   - 对小型数据集，默认使用MNN或Harmony
   - 对大型数据集，默认使用BBKNN
   - 对特殊情况如发育轨迹数据，推荐Scanorama
3. **集成策略**：

   - 添加必要的依赖项到requirements.txt
   - 创建独立的批次校正模块
   - 提供方法选择的文档和示例
4. **评估机制**：

   - 实现校正效果评估指标
   - 提供批次混合评分
   - 可视化批次校正效果

通过这些增强，ScINTEG将能够提供更灵活的批次校正选项，以适应不同类型的单细胞RNA-seq数据集和分析需求，真正整合前三个模型的所有批次校正优势。

# ScINTEG模型全面解析

ScINTEG(Single-cell Integrative Network Transformer with Explainable Gene-pathway Mappings)是一个创新的单细胞RNA测序分析模型。以下是对模型的分步骤详细解读：

## 第一步：数据加载和预处理

### 1.1 数据获取 (data_loader.py)

```
┌──────────────────────────────────┐
│         外部数据源               │
│ (PPI网络、通路数据库、基因本体)  │
└───────────────┬──────────────────┘
                │
                ▼
┌──────────────────────────────────┐
│    download_file函数             │
│  (下载和缓存外部数据库文件)      │
└───────────────┬──────────────────┘
                │
                ▼
┌──────────────────────────────────────────────────────┐
│  专门的加载函数:                                     │
│  - load_human_prior_interaction_network              │
│  - convert_human_to_mouse_network                    │
│  - load_gene_ontology                                │
│  - load_pathway_database                             │
└───────────────┬──────────────────────────────────────┘
                │
                ▼
┌──────────────────────────────────┐
│  整合的先验知识:                 │
│  - PPI网络 (基因-基因关系)       │
│  - TF调控网络                    │
│  - 基因通路映射                  │
└───────────────┬──────────────────┘
                │
                ▼
            预处理模块
```

### 1.2 数据预处理 (preprocessing.py)

```
            原始scRNA-seq数据
                │
                ▼
┌───────────────────────────────────────┐
│       quality_control_normalization   │
│  (过滤低质量细胞，标准化表达值)       │
└────────────────┬──────────────────────┘
                 │
                 ▼
┌───────────────────────────────────────┐
│            feature_selection          │
│       (选择高变基因，特征缩放)        │
└────────────────┬──────────────────────┘
                 │
                 ▼
┌───────────────────────────────────────┐
│            batch_correction           │
│  (批次效应校正: bbknn, harmony等)     │
└────────────────┬──────────────────────┘
                 │
                 ▼
┌───────────────────────────────────────┐
│          construct_dual_graphs        │
│  (构建细胞-细胞和基因-基因双图结构)   │
└────────────────┬──────────────────────┘
                 │
                 ▼
┌───────────────────────────────────────┐
│        integrate_prior_knowledge      │
│ (整合PPI网络与通路知识，创建掩码矩阵) │
└────────────────┬──────────────────────┘
                 │
                 ▼
┌───────────────────────────────────────┐
│         prepare_data_loaders          │
│       (创建模型训练所需的数据)        │
└────────────────┬──────────────────────┘
                 │
                 ▼
    处理后的AnnData与训练就绪的数据加载器
```

**关键步骤详解**：

- **质量控制与标准化**：过滤低质量细胞，标准化基因表达
- **特征选择**：识别高变基因，优化模型输入特征
- **批次校正**：支持多种方法处理批次效应
- **双图构建**：
  * 细胞图：基于KNN或共享最近邻
  * 基因图：基于先验PPI网络和相关性
- **先验知识整合**：构建基因-通路掩码矩阵

## 第二步：模型架构 (scinteg.py)

### 2.1 整体模型结构

```
ScINTEG模型
├── CellEncoder: 细胞-细胞图卷积网络
├── GeneEncoder: 基因-基因图注意力网络
│   └── GraphAttentionLayer: 自定义图注意力机制
├── PathwayProjector: 通路约束瓶颈层
├── 双解码器
│   ├── GeneDecoder: 重建基因表达矩阵
│   └── RegulatoryDecoder: 预测基因调控关系
└── TemporalAttentionLayer: 时序建模组件(可选)
```

### 2.2 数据流动

```
输入基因表达矩阵 → CellEncoder → 细胞嵌入
                           ↓
细胞嵌入 + 基因图结构 → GeneEncoder → 基因嵌入 + 注意力权重
                               ↓
基因嵌入 + 通路掩码 → PathwayProjector → 通路嵌入
                             ↓
                   ┌─────────┴────────┐
                   ↓                  ↓
通路嵌入 → GeneDecoder → 重建表达   RegulatoryDecoder → 预测的调控关系
```

### 2.3 核心组件详解

**CellEncoder**:

- 将单细胞表达矩阵投影到隐空间
- 通过GCN层编码细胞-细胞关系
- 使用残差连接和层标准化优化训练

**GeneEncoder**:

- 使用双向图注意力机制
- 支持多种注意力类型(余弦相似度、缩放点积、加性)
- 分别处理入边和出边，捕获复杂的调控关系

**PathwayProjector**:

- 创新的自适应掩码机制
- 将基因嵌入投影到通路空间
- 引入生物先验知识，同时允许数据自适应调整

**RegulatoryDecoder**:

- 基于点积和Sigmoid激活预测调控关系
- 支持负采样，提高预测准确性
- 直接从嵌入空间解码调控网络

**时序组件**:

- 处理发育轨迹和时间序列数据
- 使用时间加权的注意力机制
- 捕获基因表达随时间的动态变化

## 第三步：模型训练 (train.py)

```
       处理后的数据
            │
            ▼
┌─────────────────────────────┐
│       ScINTEGTrainer        │
│ (模型训练核心类)            │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────────────────┐
│ 多目标损失函数                          │
├─────────────────────────────────────────┤
│ - 重建损失 (MSE或交叉熵)                │
│ - 边预测损失 (二元交叉熵)               │
│ - 掩码正则化 (L1范数)                   │
│ - KL散度 (如适用)                       │
└──────────────┬──────────────────────────┘
               │
               ▼
┌─────────────────────────────────────────┐
│ 训练优化策略                            │
├─────────────────────────────────────────┤
│ - Adam优化器                            │
│ - 学习率调度 (余弦退火)                 │ 
│ - 早停机制                              │
│ - 掩码惩罚项退火                        │
│ - 课程学习 (逐步增加任务复杂度)         │
└──────────────┬──────────────────────────┘
               │
               ▼
         训练后的模型
```

**训练过程关键点**:

- **多任务学习**：同时优化多个目标函数
- **课程学习**：从简单任务(表达重建)过渡到复杂任务(网络预测)
- **惩罚退火**：随着训练逐步增加掩码正则化惩罚
- **自适应批量处理**：支持大规模数据集
- **验证和早停**：避免过拟合

## 第四步：结果分析与评估 (utils.py)

```
       训练后的模型
            │
            ▼
┌─────────────────────────────────────────────┐
│ 特征提取函数                                │
├─────────────────────────────────────────────┤
│ - extract_pathway_scores: 提取通路活性评分   │
│ - extract_attention_weights: 提取注意力权重  │
└──────────────┬──────────────────────────────┘
               │
               ▼
┌─────────────────────────────────────────────┐
│ 评估函数                                    │
├─────────────────────────────────────────────┤
│ - evaluate_network_prediction: 评估网络预测  │
│ - calculate_metrics: 计算性能指标(AUROC等)   │
└──────────────┬──────────────────────────────┘
               │
               ▼
┌─────────────────────────────────────────────┐
│ 可视化函数                                  │
├─────────────────────────────────────────────┤
│ - visualize_pathway_scores: 通路活性可视化   │
│ - visualize_latent_space: 潜在空间可视化     │
│ - visualize_attention_network: 注意力网络可视化│
└──────────────┬──────────────────────────────┘
               │
               ▼
         最终分析结果
```

**评估与分析重点**:

- **通路评分**：量化各通路在不同细胞类型中的活性
- **网络预测**：评估预测的基因调控网络准确性
- **注意力解释**：分析模型识别的基因调控关系
- **可视化工具**：多种可视化方法支持结果解读

## 第五步：实际应用流程 (example.py)

```
           开始
            │
            ▼
┌─────────────────────────────┐
│ 1. 加载单细胞数据(h5ad格式) │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 2. 获取先验知识网络         │
│ - PPI网络                   │
│ - 通路数据库                │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 3. 调用preprocess_scinteg   │
│ (完成预处理管道)            │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 4. 调用train_scinteg        │
│ (训练模型)                  │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│ 5. 结果分析                 │
│ - 提取通路评分              │
│ - 分析调控网络              │
│ - 可视化结果                │
└──────────────┬──────────────┘
               │
               ▼
         分析完成
```

**实例演示**:

- 支持自动数据下载和手动数据准备
- 展示多种批次校正方法
- 提供从预处理到结果分析的完整流程

## 模型创新点和优势

1. **多层次整合**:

   - 同时建模细胞-细胞和基因-基因关系
   - 整合基因表达、网络结构和通路知识
2. **自适应通路映射**:

   - 动态优化基因-通路关联
   - 相比静态掩码(如ExpiMap)更灵活
3. **双图学习架构**:

   - 首个同时对细胞图和基因图建模的方法
   - 提取细胞表型信息和基因调控机制
4. **注意力机制**:

   - 直接量化调控强度
   - 多种注意力类型适应不同关系模式
5. **时序建模**:

   - 结合通路约束与时间建模
   - 预测基因调控网络的动态变化
6. **不确定性感知**:

   - 为调控边预测提供置信区间
   - 帮助识别高置信度的关键调控关系

ScINTEG通过这种多层次、多目标的学习架构，为单细胞RNA测序数据分析提供了强大而灵活的计算框架，能够从原始数据中提取有生物学意义的见解，特别是在理解复杂的细胞状态和基因调控网络方面。

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/README.md

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/scinteg.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/train.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/data_loader.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/preprocessing.py

Search files...
基于文件夹内容和搜索结果，我将为您构建ScINTEG模型的有向无环图(DAG)详细解析：

# ScINTEG模型的有向无环图(DAG)详解

## 一、总体架构

```
输入数据(单细胞RNA-seq)
   |
   v
[数据预处理模块]---------------->[先验知识整合]
   |                              |
   v                              v
[细胞-细胞图构建]<------------>[基因-基因图构建]
   |                              |
   v                              v
[CellEncoder]------------------>[GeneEncoder]
   |                              |
   |                              v
   |                         [注意力权重]
   |                              |
   v                              v
[细胞嵌入]-------------------->[基因嵌入]
   |                              |
   |                              v
   |                      [PathwayProjector]
   |                              |
   |                              v
   |                        [通路嵌入空间]
   |                              |
   v                              v
[重建解码器]<-----------------[调控网络解码器]
   |                              |
   v                              v
[重建的基因表达]             [预测的调控网络]
   |                              |
   v                              v
[重建损失]                    [边预测损失]
   |                              |
   +--------------+---------------+
                  |
                  v
             [掩码正则化]
                  |
                  v
            [多目标联合损失]
                  |
                  v
             [模型优化]
                  |
                  v
             [下游应用]
```

## 二、数据预处理流程 (preprocessing.py)

```
原始单细胞数据
   |
   v
[quality_control_normalization]
   | - 过滤低质量细胞
   | - 标准化基因表达
   v
[feature_selection]
   | - 高变基因筛选
   | - 特征缩放
   v
[batch_correction]
   | - 支持多种批次校正方法
   | - BBKNN/Harmony/Scanorama/MNN
   v
[construct_dual_graphs]
   | - 构建细胞-细胞图(KNN)
   | - 构建基因-基因图(PPI+共表达)
   v
[integrate_prior_knowledge]
   | - 导入通路数据库
   | - 构建基因-通路掩码矩阵
   v
[convert_to_torch_geometric]
   | - 转换为PyG数据格式
   v
预处理完成的数据
```

## 三、模型组件及数据流 (scinteg.py)

### 1. 编码器组件

```
CellEncoder
   | 输入: 基因表达矩阵 X, 细胞-细胞边索引
   | - 线性投影层: input_dim → hidden_dim
   | - GCN卷积层: 处理细胞间关系
   | - 层归一化: 稳定训练
   | - 激活函数: GELU
   | - 残差连接: 防止梯度消失
   | 输出: 细胞嵌入 H_cell
   v

GeneEncoder
   | 输入: 细胞嵌入 H_cell, 基因-基因边索引
   | - GraphAttentionLayer: 多头自注意力
   | - 支持多种注意力机制
   |   · COS: 余弦相似度
   |   · SD: 缩放点积
   |   · AD: 加性注意力
   | 输出: 基因嵌入 H_gene, 注意力权重
   v
```

### 2. 瓶颈层

```
PathwayProjector
   | 输入: 基因嵌入 H_gene, 通路掩码矩阵
   | - 自适应掩码机制: 
   |   有效掩码 = 固定掩码 * sigmoid(自适应掩码参数)
   | - 掩码线性投影: H_gene → H_pathway
   | - 掩码惩罚项: L1正则化促进稀疏性
   | 输出: 通路嵌入 H_pathway, 掩码惩罚项
   v
```

### 3. 解码器组件

```
GeneDecoder (基因表达重建)
   | 输入: 通路嵌入 H_pathway
   | - 多层感知机: latent_dim → hidden_dim → input_dim
   | - GELU激活函数
   | - Dropout正则化
   | 输出: 重建的基因表达矩阵 X_recon
   v

RegulatoryDecoder (调控网络预测)
   | 输入: 基因嵌入 H_gene, 基因-基因边索引
   | - 内积操作: 计算源节点与目标节点嵌入的相似性
   | - Sigmoid激活: 转换为边的存在概率
   | - 负采样策略: 处理网络稀疏性
   | 输出: 预测的基因调控关系概率
   v

TemporalAttentionLayer (可选时序组件)
   | 输入: 通路嵌入 H_pathway, 时间信息
   | - 时间特异性注意力计算
   | - 时序平滑约束
   | 输出: 时序调整后的通路嵌入
   v
```

## 四、训练流程 (train.py)

```
ScINTEGTrainer
   | 输入: 模型实例, 预处理数据
   | 
   | [损失函数组合]
   | - 重建损失(lambda_recon): MSE或交叉熵
   | - 边预测损失(lambda_edge): 二元交叉熵 
   | - 掩码正则化(lambda_mask): L1范数
   | - KL散度(lambda_kl): 如适用
   |
   | [优化策略]
   | - Adam优化器
   | - 余弦退火学习率
   | - 早停机制
   | - 课程学习: 
   |   · 逐步增加复杂损失项权重
   |   · 掩码惩罚逐渐加强
   |
   | 输出: 训练好的模型, 训练历史
   v
```

## 五、数据加载和知识整合 (data_loader.py)

```
先验知识获取
   |
   +--->[load_human_prior_interaction_network]
   |     | - 整合TF-基因调控网络
   |     | - 支持TRRUST/RegNetwork/ChEA
   |
   +--->[load_gene_ontology]
   |     | - 获取基因本体信息
   |     | - 构建功能基因集
   |
   +--->[load_pathway_database]
   |     | - 获取通路数据库
   |     | - MSigDB/KEGG/Reactome
   |
   +--->[download_ppi_network]
         | - 获取蛋白质互作网络
         | - STRING/BioGRID
         v
```

## 六、核心创新点

1. **双图架构**:

   - 同时建模细胞-细胞和基因-基因关系
   - 细胞视角和基因视角的知识整合
2. **自适应通路掩码**:

   - 掩码线性投影优于静态掩码
   - 数据驱动的基因-通路映射优化
3. **多头注意力机制**:

   - 揭示复杂的基因调控关系
   - 注意力权重直接量化调控强度
4. **多目标联合优化**:

   - 同时优化表达重建和网络推断
   - 课程学习策略平衡不同任务
5. **时序建模能力**:

   - 可选的时序处理组件
   - 捕获基因表达随时间的动态变化

## 七、下游应用

```
训练后的模型
   |
   +--->[细胞类型识别]
   |     | - 无监督聚类
   |     | - 监督分类
   |
   +--->[通路活性评分]
   |     | - 计算样本-通路活性矩阵
   |     | - 功能富集分析
   |
   +--->[基因调控网络构建]
   |     | - 推断TF-基因调控关系
   |     | - 鉴定关键调控因子
   |
   +--->[扰动模拟]
         | - 预测基因敲除效应
         | - 模拟药物治疗反应
```

这个DAG全面展示了ScINTEG模型的架构和数据流向，突出了其核心创新点和组件间的交互关系，清晰地呈现了从输入数据到最终应用的完整流程。

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/example.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/__init__.py

Search files...

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/scinteg.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/train.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/utils.py

# ScINTEG模型函数流程的有向无环图

## 一、顶层流程

```
1. 数据加载与预处理
   └─ preprocess_scinteg()
      ├─ quality_control_normalization()
      ├─ feature_selection()
      ├─ batch_correction()
      ├─ construct_dual_graphs()
      ├─ integrate_prior_knowledge()
      └─ prepare_data_loaders()
           |
           v
2. 模型定义与初始化
   └─ ScINTEG()
      ├─ CellEncoder()
      ├─ GeneEncoder()
      │  └─ GraphAttentionLayer()
      ├─ PathwayProjector()
      ├─ GeneDecoder()
      ├─ RegulatoryDecoder()
      └─ TemporalAttentionLayer() (可选)
           |
           v
3. 模型训练
   └─ train_scinteg()
      └─ ScINTEGTrainer.train()
           |
           v
4. 结果分析与可视化
   ├─ extract_pathway_scores()
   ├─ extract_attention_weights()
   ├─ evaluate_network_prediction()
   ├─ visualize_pathway_scores()
   └─ visualize_attention_network()
```

## 二、数据预处理流程

```
preprocess_scinteg()
    |
    ├─ download_ppi_network() ──────┐
    |   (从STRING等数据库获取PPI网络) |
    |                                |
    ├─ load_pathway_database() ─────┤
    |   (加载通路信息)               |
    |                                |
    ├─ load_human_prior_interaction_network() ─┤
    |   (加载TF-基因调控关系)                  |
    |                                          v
    ├─ quality_control_normalization() ────> normalize_data()
    |   (细胞质控、标准化)                     |
    |                                          v
    ├─ feature_selection() ──────────────> highly_variable_genes()
    |   (高变基因筛选)                         |
    |                                          v
    ├─ batch_correction() ──────┬───────> bbknn()
    |   (批次效应校正)          |
    |                           ├───────> harmony()
    |                           |
    |                           ├───────> scanorama()
    |                           |
    |                           └───────> mnn()
    |                                      |
    ├─ construct_dual_graphs() ─┬───────> build_cell_graph()
    |   (构建双图结构)          |
    |                           └───────> build_gene_graph()
    |                                      |
    ├─ integrate_prior_knowledge() ────> get_pathway_gene_mask()
    |   (整合先验知识)                      |
    |                                       v
    └─ prepare_data_loaders() ────────> convert_to_torch_geometric()
        (准备训练数据)
```

## 三、模型结构与前向传播

```
ScINTEG.__init__()
    |
    ├─ CellEncoder.__init__()
    ├─ GeneEncoder.__init__()
    │   └─ GraphAttentionLayer.__init__()
    ├─ PathwayProjector.__init__()
    ├─ GeneDecoder.__init__() (nn.Sequential)
    ├─ RegulatoryDecoder.__init__()
    └─ TemporalAttentionLayer.__init__() (可选)
        |
        v
ScINTEG.forward()
    |
    ├─ CellEncoder.forward()
    |   └─ GCNConv(): 处理细胞-细胞关系
    |       |
    |       v
    ├─ GeneEncoder.forward()
    |   ├─ GraphAttentionLayer.forward(): 处理入边
    |   └─ GraphAttentionLayer.forward(): 处理出边
    |       |
    |       v
    ├─ PathwayProjector.forward()
    |   └─ 应用基因-通路掩码
    |       |
    |       v
    ├─ TemporalAttentionLayer.forward() (可选)
    |   └─ 处理时序信息
    |       |
    |       v
    ├─ GeneDecoder() (Sequential): 重建基因表达
    └─ RegulatoryDecoder.forward(): 预测基因调控关系
        |
        v
    返回: {
        'reconstructed_x': 重建的基因表达,
        'gene_interactions': 预测的调控关系,
        'pathway_emb': 通路嵌入,
        'attention_weights': 注意力权重,
        'mask_penalty': 掩码惩罚项
    }
```

## 四、训练流程

```
train_scinteg()
    |
    ├─ ScINTEG() 初始化模型
    └─ ScINTEGTrainer.train()
        |
        ├─ convert_to_torch_geometric() 转换数据格式
        |
        ├─ Adam() 创建优化器
        |
        ├─ CosineAnnealingLR() 学习率调度器
        |
        └─ 训练循环
            |
            ├─ model.train() 设置训练模式
            |
            ├─ 处理每个批次
            |   |
            |   ├─ model.forward() 前向传播
            |   |
            |   ├─ 计算损失函数
            |   |   ├─ 重建损失(lambda_recon)
            |   |   ├─ 边预测损失(lambda_edge)
            |   |   ├─ 掩码惩罚项(lambda_mask)
            |   |   └─ KL散度(lambda_kl)
            |   |
            |   ├─ loss.backward() 反向传播
            |   |
            |   └─ optimizer.step() 更新参数
            |
            ├─ scheduler.step() 更新学习率
            |
            ├─ _validate() 在验证集上评估
            |
            ├─ 早停检查
            |
            └─ 保存检查点
                |
                v
            返回: model, history
```

## 五、评估与分析

```
extract_pathway_scores()
    |
    ├─ model.eval() 设置评估模式
    |
    ├─ 提取图结构信息
    |
    ├─ 分批处理数据
    |   |
    |   └─ model.forward() 前向传播
    |       |
    |       └─ 提取通路嵌入
    |
    └─ 返回: 通路活性得分矩阵 (cells × pathways)
        |
        v
visualize_pathway_scores()
    |
    ├─ 计算每个细胞类型的平均通路活性
    |
    ├─ 选择差异最大的通路
    |
    └─ 绘制热图
        |
        v
extract_attention_weights()
    |
    ├─ model.eval() 设置评估模式
    |
    ├─ 提取基因名称
    |
    ├─ model.forward() 前向传播
    |
    └─ 返回: 注意力权重矩阵, 源基因列表, 目标基因列表
        |
        v
visualize_attention_network()
    |
    ├─ 筛选强调控关系
    |
    ├─ 构建网络图
    |
    └─ 可视化调控网络
        |
        v
evaluate_network_prediction()
    |
    ├─ model.eval() 设置评估模式
    |
    ├─ 计算正样本和负样本预测
    |
    ├─ 计算评估指标
    |   ├─ AUROC (曲线下面积)
    |   └─ AUPRC (精确率-召回率曲线下面积)
    |
    └─ 返回: 性能指标
```

## 六、辅助工具函数

```
build_cell_graph()
    |
    ├─ sc.pp.neighbors() 计算KNN图
    |
    └─ 返回: 细胞-细胞边索引
        |
        v
build_gene_graph()
    |
    ├─ 整合PPI网络和共表达信息
    |
    └─ 返回: 基因-基因边索引
        |
        v
get_pathway_gene_mask()
    |
    ├─ 加载通路基因集
    |
    ├─ 构建二元掩码矩阵
    |
    └─ 返回: 通路-基因掩码
        |
        v
save_model()
    |
    └─ 保存模型和配置
        |
        v
load_model()
    |
    └─ 加载模型和配置
```

## 七、数据加载函数

```
download_file()
    |
    ├─ 检查文件是否存在
    |
    ├─ 下载并保存文件
    |
    └─ 解压(如果是压缩文件)
        |
        v
load_human_prior_interaction_network()
    |
    ├─ download_file() 下载数据
    |
    ├─ 处理TRRUST数据
    |
    ├─ 处理RegNetwork数据
    |
    ├─ 处理ChEA数据
    |
    └─ 返回: TF-基因调控网络
        |
        v
convert_human_to_mouse_network()
    |
    ├─ get_human_to_mouse_gene_mapping()
    |
    └─ 返回: 小鼠的TF-基因网络
        |
        v
load_gene_ontology()
    |
    ├─ download_file() 下载GO数据
    |
    ├─ 解析GO注释
    |
    └─ 返回: GO通路基因集
        |
        v
load_pathway_database()
    |
    ├─ 下载MSigDB/KEGG/Reactome数据
    |
    ├─ 解析通路信息
    |
    └─ 返回: 通路基因集
```

## 八、示例使用流程 (example.py)

```
main()
    |
    ├─ sc.read_h5ad() 加载数据
    |
    ├─ preprocess_scinteg() 预处理数据
    |   |
    |   ├─ 自动下载PPI网络和通路数据
    |   └─ 返回: 处理后的数据和加载器
    |
    ├─ train_scinteg() 训练模型
    |   |
    |   └─ 返回: 训练后的模型和历史
    |
    ├─ extract_pathway_scores() 提取通路活性
    |
    ├─ extract_attention_weights() 提取注意力权重
    |
    ├─ evaluate_network_prediction() 评估网络预测
    |
    ├─ visualize_pathway_scores() 可视化通路活性
    |
    └─ visualize_attention_network() 可视化调控网络
```

这个有向无环图全面呈现了ScINTEG模型中所有主要函数的调用流程和数据流向，清晰地展示了从数据加载、预处理、模型构建、训练到结果分析的完整流程。

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/scinteg.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/scinteg.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/preprocessing.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/preprocessing.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/preprocessing.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/train.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/data_loader.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/utils.py

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/requirements.txt

Read file: /mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/ScINTEG/setup.py

# ScINTEG（单细胞整合网络转换器）代码详细解读

## 1. 整体架构与核心概念

ScINTEG是一个用于单细胞RNA-seq数据分析的深度学习框架，它通过整合单细胞表达数据、基因调控网络和生物通路知识，实现了多层次的单细胞分析。该框架采用了双重图架构，分别建模细胞-细胞关系和基因-基因关系。

### 核心技术特点：

- **双重图神经网络**：同时处理细胞图和基因图
- **通路约束的瓶颈层**：将基因空间压缩到生物学相关的通路空间
- **多头注意力机制**：捕捉复杂的基因间交互关系
- **时序建模能力**：支持时间序列单细胞数据分析

## 2. 模型架构（scinteg.py）

### 主模型：ScINTEG

这是框架的核心类，整合了所有组件：

```python
def __init__(self, 
             input_dim, 
             hidden_dim=128, 
             latent_dim=64,
             pathway_mask=None,
             n_conditions=1,
             n_heads=4,
             dropout=0.1,
             attention_type='COS',
             use_temporal=False):
```

模型的前向传播过程包含五个关键步骤：

1. **细胞编码**：使用CellEncoder将细胞表达数据映射到隐空间
2. **基因编码**：通过GeneEncoder处理基因间关系
3. **通路瓶颈**：通过PathwayProjector将基因空间压缩到通路空间
4. **时序处理**：（可选）处理时间序列信息
5. **双重解码**：重建基因表达并预测基因间调控关系

### 关键组件解析：

#### CellEncoder

使用图卷积网络处理细胞-细胞关系：

```python
def forward(self, x, edge_index, batch=None):
    x = self.input_projection(x)
    for i, (conv, norm) in enumerate(zip(self.conv_layers, self.norm_layers)):
        x_res = x
        x = conv(x, edge_index)
        x = norm(x)
        x = self.activation(x)
        x = self.dropout(x)
        if i > 0:  # 第一层后添加残差连接
            x = x + x_res
    return x
```

#### GraphAttentionLayer

实现了三种不同的注意力机制：

- **COS**：基于余弦相似度
- **SD**：缩放点积
- **AD**：加性注意力（类似GAT）

#### GeneEncoder

处理基因-基因图，兼顾入边和出边：

```python
def forward(self, x, edge_index):
    x = self.input_norm(x)
  
    # 处理入边和出边
    x_in, att_in = self.att_in(x, edge_index)
    x_out, att_out = self.att_out(x, edge_index)
  
    # 合并特征
    x_combined = torch.cat([x_in.flatten(1), x_out.flatten(1)], dim=1)
    out = self.combine(x_combined)
  
    # 合并注意力权重
    attention_weights = 0.5 * att_in + 0.5 * att_out
  
    return out, attention_weights
```

#### PathwayProjector

将基因空间压缩到生物通路空间，使用掩码矩阵约束映射关系：

```python
def forward(self, x, mask=None):
    # 如果提供掩码则应用
    if mask is not None:
        masked_weight = self.weight * mask
    else:
        masked_weight = self.weight
  
    # 线性变换
    out = F.linear(x, masked_weight, self.bias)
    return out
```

#### RegulatoryDecoder

预测基因间调控关系：

```python
def forward(self, z, edge_index, sigmoid=True):
    # 提取源节点和目标节点索引
    src, dst = edge_index
  
    # 计算节点嵌入的点积
    value = (z[src] * z[dst]).sum(dim=1)
  
    # 应用sigmoid（如果需要）
    if sigmoid:
        return torch.sigmoid(value)
  
    return value
```

#### TemporalAttentionLayer

处理时间序列数据的注意力层：

```python
def forward(self, x, time_info):
    # 重塑时间信息以便广播
    time_info = time_info.view(-1, 1)
  
    # 时间感知的注意力机制
    q = self.query(x)
    k = self.key(x) * time_info  # 根据时间缩放key
    v = self.value(x)
  
    # 计算注意力分数
    scores = torch.bmm(q.unsqueeze(1), k.unsqueeze(2)).squeeze()
    attention = torch.softmax(scores, dim=1)
  
    # 应用注意力
    out = x * attention.unsqueeze(-1)
  
    return out
```

## 3. 数据预处理（preprocessing.py）

数据预处理模块包含完整的单细胞数据处理流程：

### 质量控制和归一化

```python
def quality_control_normalization(
    adata: AnnData, 
    min_genes: int = 200, 
    min_cells: int = 3,
    max_mt_percent: float = 20.0, 
    norm_method: str = 'log1p'
) -> AnnData:
```

- 过滤低质量细胞和基因
- 移除线粒体比例过高的细胞
- 支持多种归一化方法

### 特征选择

选择高变异基因进行下游分析：

```python
def feature_selection(
    adata: AnnData, 
    n_top_genes: int = 2000,
    use_highly_variable: bool = True,
    scale: bool = True
) -> AnnData:
```

### 批次效应校正

支持多种批次校正方法：

- BBKNN
- Harmony
- Scanorama
- MNN

### 构建双重图结构

```python
def construct_dual_graphs(
    adata: AnnData,
    ppi_network: Optional[pd.DataFrame] = None,
    n_neighbors: int = 15,
    add_self_loops: bool = True,
    prune_threshold: float = 0.1,
    batch_key: Optional[str] = None
) -> AnnData:
```

同时构建细胞-细胞图和基因-基因图结构

### 整合先验知识

```python
def integrate_prior_knowledge(
    adata: AnnData,
    pathway_database: str = 'custom',
    custom_pathways: Optional[Dict[str, List[str]]] = None,
    tf_list: Optional[List[str]] = None,
    min_genes_per_pathway: int = 5,
    max_genes_per_pathway: int = 500
) -> AnnData:
```

- 整合生物通路信息
- 标注转录因子
- 创建通路掩码矩阵

## 4. 数据加载（data_loader.py）

负责下载和处理外部数据源：

### 基因网络下载

```python
def download_file(url: str, output_path: str, show_progress: bool = True) -> str:
```

### 加载人类基因交互网络

```python
def load_human_prior_interaction_network(
    data_dir: str = './data',
    network_types: List[str] = ['trrust', 'regnetwork', 'chea'],
    include_mirna: bool = False
) -> Tuple[pd.DataFrame, Dict]:
```

支持多种数据库：TRRUST、RegNetwork等

### 人类网络转换为小鼠网络

```python
def convert_human_to_mouse_network(
    human_network: pd.DataFrame,
    data_dir: str = './data',
    keep_nonconverted: bool = True,
    hgnc_complete: bool = False
) -> pd.DataFrame:
```

使用HGNC和MGI数据进行同源基因映射

### 加载基因本体论数据

```python
def load_gene_ontology(
    data_dir: str = './data',
    go_category: str = 'biological_process',
    species: str = 'human',
    min_genes: int = 5,
    max_genes: int = 500
) -> Dict[str, List[str]]:
```

## 5. 模型训练（train.py）

### ScINTEGTrainer

训练器类，负责模型的训练、验证和优化：

```python
def train(
    self,
    adata: AnnData,
    data_loaders: List[Dict],
    epochs: int = 150,
    lr: float = 1e-3,
    weight_decay: float = 1e-6,
    lambda_recon: float = 1.0,
    lambda_edge: float = 0.5,
    lambda_mask: float = 0.01,
    lambda_kl: float = 0.1,
    patience: int = 15,
    validation_split: float = 0.1,
    curriculum_learning: bool = True,
) -> Dict:
```

训练过程特点：

- **课程学习**：逐步增加复杂任务的权重
- **早停**：防止过拟合
- **多任务损失**：
  - 重建损失
  - 边预测损失
  - 掩码正则化
  - KL散度（变分推断）

### 边损失计算

```python
def _compute_edge_loss(self, edge_preds, edge_index, pos_weight=1.0):
```

使用负采样生成负例，计算二元交叉熵损失

## 6. 工具函数（utils.py）

实用工具函数，用于模型分析和可视化：

### 网络预测评估

```python
def evaluate_network_prediction(
    model,
    adata: Optional[Union[ad.AnnData, np.ndarray, torch.Tensor]] = None,
    test_edge_index: torch.Tensor = None,
    negative_edge_index: Optional[torch.Tensor] = None,
    gene_emb: Optional[torch.Tensor] = None,
    metrics: List[str] = ['auroc', 'auprc'],
) -> Dict[str, Union[float, np.ndarray]]:
```

### 通路得分提取

```python
def extract_pathway_scores(
    model,
    adata: ad.AnnData,
    cell_edge_index: Optional[torch.Tensor] = None,
    gene_edge_index: Optional[torch.Tensor] = None,
    use_highly_variable: bool = True,
    batch_size: int = 512,
    device: Union[str, torch.device] = 'cuda'
) -> pd.DataFrame:
```

### 注意力权重提取

```python
def extract_attention_weights(
    model,
    adata: ad.AnnData,
    device: Union[str, torch.device] = 'cuda'
) -> Tuple[np.ndarray, List[str], List[str]]:
```

### 通路得分可视化

```python
def visualize_pathway_scores(
    pathway_scores: pd.DataFrame,
    adata: ad.AnnData,
    cluster_key: str = 'leiden',
    n_top_pathways: int = 20,
    figsize: Tuple[int, int] = (12, 8),
    save_path: Optional[str] = None
) -> plt.Figure:
```

## 7. 数据流分析

### 完整处理流程：

1. **数据输入**：单细胞表达矩阵（AnnData对象）
2. **预处理**：
   - 质量控制
   - 归一化
   - 特征选择
   - 批次校正
3. **网络构建**：
   - 细胞-细胞图（基于相似性）
   - 基因-基因图（基于先验知识）
4. **模型训练**：
   - 表达重建
   - 网络预测
   - 通路映射学习
5. **结果分析**：
   - 通路活性评分
   - 调控网络预测
   - 注意力权重分析

### 数据依赖关系：

- **表达数据** → **细胞编码器** → **细胞嵌入**
- **细胞嵌入** + **基因-基因关系** → **基因编码器** → **基因嵌入**
- **基因嵌入** + **通路掩码** → **通路投影器** → **通路嵌入**
- **通路嵌入** → **基因解码器** → **重建表达**
- **基因嵌入** → **调控解码器** → **预测基因交互**

## 8. 技术亮点总结

1. **多层次整合**：融合细胞级和基因级信息
2. **生物先验约束**：利用已知的通路和调控网络知识
3. **自注意力机制**：高效处理复杂的交互关系
4. **可解释性设计**：通路映射提供生物学解释
5. **灵活的批次校正**：支持多种最新批次效应校正方法
6. **可扩展性**：模块化设计，支持处理大规模数据

该框架为单细胞数据分析提供了一个强大的工具，特别适合研究基因调控网络、细胞状态转换和通路活性分析，是单细胞组学和网络生物学的有力结合。

# pathway的掩码问题

Read file: /f:/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/idea/3model/expimap/expimap_model.py

Read file: 3model/expimap/expimap_model.py

Read file: 3model/expimap/expimap.py

Read file: 3model/expimap/modules.py

Read file: 3model/expimap/losses.py
基于对ScINTEG和ExpiMap代码的分析，现在我可以对两个模型的pathway掩码处理方法进行详细比较：

## SCINTEG与ExpiMap的pathway掩码处理对比

### 1. 掩码创建与存储

**相同点**：

- 两个模型都使用二元矩阵表示基因与pathway的关系
- 都将掩码存储在AnnData对象中（ScINTEG使用 `adata.varm['pathway_mask']`，ExpiMap使用 `adata.varm[mask_key]`）

**不同点**：

- ScINTEG默认掩码形状为(n_pathways, n_genes)，而ExpiMap期望形状为(n_genes, n_pathways)，初始化时会转置
- ExpiMap在模型参数中明确提供了mask_key选项，方便用户指定不同的掩码名称

### 2. 掩码应用方式

**相同点**：

- 两个模型都使用掩码来约束从潜在空间到基因表达空间的映射
- 都实现了固定掩码和自适应掩码的结合

**不同点**：

- **掩码集成位置**：

  - ScINTEG在 `PathwayProjector`层应用掩码，处于编码器之后，解码器之前的瓶颈层
  - ExpiMap在 `MaskedLinear`类中应用掩码，直接约束权重矩阵
- **掩码自适应方式**：

  - ScINTEG：使用 `effective_mask = fixed_mask * torch.sigmoid(adaptive_mask)`，adaptive_mask初始值为0.1
  - ExpiMap：提供了 `soft_mask`选项，可以在完全硬约束和L1正则化软约束之间选择
- **掩码扩展能力**：

  - ExpiMap提供了更灵活的扩展能力，通过 `n_ext`和 `n_ext_m`参数支持无约束和有约束的额外项
  - ScINTEG没有显式的扩展机制

### 3. 正则化与学习

**相同点**：

- 两个模型都使用某种形式的正则化来优化掩码

**不同点**：

- **正则化策略**：

  - ScINTEG：使用简单的L1正则化 `mask_penalty = torch.sum(torch.abs(adaptive_mask))`
  - ExpiMap：实现了更复杂的正则化策略，包括组L1正则化(Group Lasso)和HSIC(Hilbert-Schmidt Independence Criterion)正则化
- **训练机制**：

  - ExpiMap提供了更详细的训练参数调整，如 `alpha_l1_epoch_anneal`用于逐步增加掩码正则化权重
  - ScINTEG简单地使用固定的 `lambda_mask`参数

### 4. 架构整合

**相同点**：

- 两个模型都将掩码作为模型架构的核心组件

**不同点**：

- **模型复杂度**：

  - ExpiMap设计更复杂，有更多的参数和选项用于控制掩码行为
  - ScINTEG设计更直观简洁，掩码应用更为直接
- **条件处理**：

  - ExpiMap设计了专门的条件处理系统，可以处理不同批次或条件
  - ScINTEG更专注于单一数据集的pathway分析

### 5. 综合比较

ScINTEG的pathway掩码处理更为直接，强调在瓶颈层对gene-pathway关系的约束，通过简单的自适应机制允许模型微调掩码。

ExpiMap则提供了更复杂的掩码处理框架，支持硬掩码和软掩码模式，并有更丰富的扩展选项，允许模型发现新的、未在已知pathway中定义的关系。

这些区别反映了两个模型的设计哲学不同：ScINTEG更注重直观和稳健的pathway表示，而ExpiMap则更关注复杂数据集的灵活建模和跨条件分析。

在scNET模型中，基因特征(gene_feature)的计算主要在 `build_network`函数中实现。让我详细分析这个过程：

Read file: 3model/scNET/main.py

Read file: 3model/scNET/main.py

# scNET中基因特征(gene_feature)的计算方法

在scNET模型中，基因特征(gene_feature)的计算是通过 `build_network`函数实现的，这是模型中一个核心步骤。下面我将详细解释计算过程：

## 1. 基因特征提取过程

基因特征的计算分为以下几个关键步骤：

### 步骤1: 获取网络中出现的基因列表

```python
# 从网络中提取所有唯一的基因名称
genes = list(pd.concat([net.Source, net.Target]).drop_duplicates())
# 从单细胞数据中筛选存在的基因
genes = obj.var[obj.var.index.isin(genes)].index
```

这一步首先从基因-基因交互网络（来自外部数据库如BIOGRID或其他人类相互作用数据）中提取所有基因名称，然后确保这些基因在单细胞数据集中存在。

### 步骤2: 提取基因表达数据作为特征

```python
# 从原始表达矩阵中提取这些基因的表达值
node_feature = sc.get.obs_df(obj.raw.to_adata(), list(genes)).T
```

此步骤使用scanpy库的 `get.obs_df`函数从AnnData对象的原始表达矩阵中提取指定基因的表达数据。关键点是进行了转置(`.T`)操作，将原始的细胞×基因矩阵转换为基因×细胞矩阵，这样每一行代表一个基因，每一列代表一个细胞。

### 步骤3: 过滤低表达基因

```python
# 计算每个基因在多少细胞中表达
node_feature["non_zero"] = node_feature.apply(lambda x: x.astype(bool).sum(), axis=1)
# 过滤掉在太少细胞中表达的基因
node_feature = node_feature.loc[node_feature.non_zero > node_feature.shape[1] * EXPRESSION_CUTOFF]
node_feature.drop("non_zero", axis=1, inplace=True)
```

这一步执行质量控制：

1. 为每个基因计算在多少细胞中有表达（非零值的数量）
2. 只保留在足够多细胞中表达的基因（由EXPRESSION_CUTOFF参数控制，默认为0）
3. 删除临时的"non_zero"列

### 步骤4: 根据网络进一步过滤

```python
# 过滤网络边，只保留有效基因之间的交互
net = net.loc[net.Source != net.Target]  # 移除自环
net = net.loc[net.Source.isin(node_feature.index)]  # 源基因必须在过滤后的基因中
net = net.loc[net.Target.isin(node_feature.index)]  # 目标基因必须在过滤后的基因中

# 构建网络图
gp = nx.from_pandas_edgelist(net, "Source", "Target")

# 最终只保留网络中存在的基因的特征
node_feature = node_feature.loc[list(gp.nodes)]
```

这一步确保基因特征和网络结构之间的一致性：

1. 从交互网络中移除自环（基因自己与自己的交互）
2. 只保留过滤后基因集合中的交互
3. 构建网络图
4. 最终的基因特征矩阵只包含网络中实际存在的基因

## 2. 基因特征的数学表示

最终的基因特征是一个基因×细胞的表达矩阵，其中:

- 行: 表示基因（与网络中的节点对应）
- 列: 表示细胞
- 值: 单细胞RNA数据中的表达值（通常是经过归一化和对数转换的）

在后续处理中，这个特征矩阵被转换为张量并用于模型训练：

```python
x = node_feature.values
x = torch.tensor(x, dtype=torch.float32).cpu()
```

## 3. 基因特征在模型中的使用

在scNET模型中，基因特征有两个主要用途：

1. **作为GNN初始节点特征**：每个基因（行）的表达向量作为图神经网络中该基因节点的特征
2. **在训练过程中被归一化处理**：

   ```python
   x = ((x.T - (x.mean(axis=1))) / (x.std(axis=1) + 0.00001)).T
   ```

   这确保了每个细胞的基因表达分布被标准化，有助于模型训练的稳定性。

## 4. 特征提取的生物学意义

这种特征提取方法具有几个重要的生物学意义：

1. **整合先验知识与实验数据**：通过将基因-基因交互网络（先验知识）与单细胞表达数据（实验数据）结合，模型能够学习到更有生物学意义的表示。
2. **双模态表示**：每个基因通过两个维度表示 - 其网络拓扑结构（基因-基因交互）和表达模式（在不同细胞中的表达）。
3. **关注生物学相关基因**：通过仅包含存在于交互网络中的基因，模型专注于那些已知参与生物功能的基因，减少了噪声。
4. **质量控制**：过滤步骤确保只有在足够多细胞中表达的基因被考虑，增强了特征的可靠性。

总结来说，scNET模型中的基因特征不仅仅是单纯的表达数据，而是经过精心设计的处理流程，将基因表达与网络结构紧密结合，形成了一个包含丰富生物学信息的特征表示，为后续的神经网络学习奠定了基础。

# CEFCON中基因特征的获取与处理

在CEFCON模型中，基因编码的gene_feature计算方式相对简单而直接。从代码分析可以看出，基因特征主要来源于以下方式：

## 1. 基因特征的获取与处理 (`NetModel._get_graph_data`)

```python
def _get_graph_data(self, data):
    """从AnnData对象中获取基因特征和网络结构"""
  
    # 获取基因表达数据作为基本特征
    if isinstance(data.X, sparse.csr_matrix):
        nodes = data.X.A.T  # 从稀疏矩阵转为稠密矩阵
    else:
        nodes = data.X.T  # 直接使用表达矩阵转置作为初始特征
  
    # 提取边列表
    edges = data.uns['edgelist']
    edges = np.array([edges.iloc[:, 0].values, edges.iloc[:, 1].values])
  
    # 获取辅助特征（差异表达分数）
    if 'node_score_auxiliary' in data.var:
        node_score_auxiliary = np.array(data.var['node_score_auxiliary'])
    else:
        # 如果没有提供差异表达分数，则创建全1向量
        node_score_auxiliary = np.ones(nodes.shape[0])
  
    # 存储基因名称
    self._genes = data.var_names.values
  
    return nodes, edges, node_score_auxiliary
```

## 2. 特征计算流程

1. **基本特征表示**:

   - 直接使用基因表达矩阵转置作为初始特征
   - 每个基因的特征是其在所有细胞中的表达值向量
   - 矩阵形状为 [n_genes × n_cells]
2. **没有复杂的特征工程**:

   - CEFCON不对初始特征进行特殊变换或编码
   - 依靠图神经网络自动学习合适的特征表示
3. **特征变换过程**:

   - 在GNN的第一层，原始特征经过线性投影进入隐藏空间

   ```python
   def forward(self, x, edge_index, x_auxiliary, return_attention=False):
       # 线性投影与GELU激活
       x = self.act(self.x_input(x))
   ```

## 3. 辅助特征处理

差异表达信息作为重要的辅助特征输入:

```python
# 在前向传播中处理辅助特征
x_auxiliary = torch.sigmoid(self.c * (x_auxiliary - self.d))
```

此处的 `x_auxiliary`是基因的差异表达分数，通过sigmoid函数进行校准，参数c和d控制曲线形状，使其更能区分重要和不重要的基因。

## 4. 特征传播与更新

在图神经网络中，基因特征通过注意力机制在网络中传播:

```python
# 入向注意力传播（被调控关系）
x_in, (_, att_weights_in) = layer[1](x, edge_index, x_auxiliary, return_attention_weights=True)

# 出向注意力传播（调控关系）  
x_out, (_, att_weights_out) = layer[2](x, edge_index, x_auxiliary, return_attention_weights=True)

# 特征融合
x_fused = torch.cat([x_in, x_out], dim=1)
x = x + layer[3](x_fused)  # 残差连接
```

## 5. 最终基因嵌入

模型训练后，获取最终的基因嵌入:

```python
def get_gene_embedding(self, output_file=None):
    """提取基因嵌入向量"""
    embeddings = np.mean(np.dstack(self._x_embs), axis=2)  # 平均多次训练的结果
    gene_embs = pd.DataFrame(embeddings, index=self._genes)
  
    return gene_embs
```

## 总结

CEFCON的基因特征表示方法有以下特点：

1. **简单直接**：直接使用基因表达向量作为初始特征
2. **表达驱动**：特征直接来源于单细胞表达数据
3. **集成差异信息**：通过辅助特征整合差异表达信息
4. **自动特征学习**：依靠GNN自动学习特征表示
5. **双向信息融合**：通过图结构融合入向和出向信息

这种设计允许CEFCON模型在不需要复杂特征工程的情况下，有效捕获基因之间的调控关系，学习基因在特定细胞谱系中的功能角色。

# 重新设计

好的，综合考虑您之前讨论的 expimap, scNET 和 CEFCON 模型，并结合您对 ScINTEG 的理解，我对 ScINTEG 的设计进行了重新构思和润色，并以有向无环图 (DAG) 的形式呈现如下。

这个重新设计的 ScINTEG 旨在融合各个模型的优点，同时解决您可能之前关注的一些问题，例如缺乏细胞解码机制等。

### 重新设计的 ScINTEG 模型 (有向无环图 DAG)

以下是有向无环图形式的 ScINTEG 模型设计，每个节点代表一个处理步骤或模块，箭头表示数据流的方向。

```mermaid
graph LR
    A[输入: 单细胞 RNA-seq 数据 & 先验生物网络] --> B(数据预处理);
    B --> C{基因特征提取};
    C --> D[通路掩码构建];
    D --> E(基因编码器);
    E --> F{自适应网络精简};
    F --> G(细胞解码器);
    G --> H{调控基因模块 (RGM) 识别};
    H --> I[输出: 潜在基因表示, 细胞表示, 精简基因网络, RGMs];

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style I fill:#ccf,stroke:#333,stroke-width:2px
    style B fill:#eee,stroke:#333,stroke-width:1px
    style C fill:#eee,stroke:#333,stroke-width:1px
    style D fill:#eee,stroke:#333,stroke-width:1px
    style E fill:#eee,stroke:#333,stroke-width:1px
    style F fill:#eee,stroke:#333,stroke-width:1px
    style G fill:#eee,stroke:#333,stroke-width:1px
    style H fill:#eee,stroke:#333,stroke-width:1px
```

**DAG 节点解释:**

* **A. 输入: 单细胞 RNA-seq 数据 & 先验生物网络:**

  * **单细胞 RNA-seq 数据:**  原始的基因表达矩阵，包含细胞和基因的表达水平。
  * **先验生物网络:**  例如通路数据库 (KEGG, GO), 基因相互作用网络等，用于指导模型学习和通路分析。
* **B. 数据预处理 (灵感来自 CEFCON):**

  * **质量控制 (QC):**  过滤低质量细胞和基因，去除噪声数据。
  * **标准化 (Normalization):**  例如 CPM, TPM, SCTransform 等方法，消除测序深度和细胞大小差异。
  * **特征选择/基因过滤:**  选择高变基因或与生物学问题相关的基因，降低数据维度，提高计算效率。
  * **可选的批次效应校正:**  如果数据来自多个批次，可以使用 ComBat, Harmony 等方法进行批次效应校正。
  * *借鉴 CEFCON 的预处理流程，确保数据质量和一致性。*
* **C. 基因特征提取 (灵感来自 scNET):**

  * **计算基因特征:**  从预处理后的基因表达数据中提取基因的特征表示。这可以包括基因的表达水平统计特征 (均值, 方差等), 基因之间的共表达模式，或者更复杂的基于图神经网络的特征提取方法。
  * *可以考虑借鉴 scNET 中基因特征计算的方法，例如利用基因之间的网络关系来增强特征表示。*
* **D. 通路掩码构建 (ScINTEG 原有优势, 可增强):**

  * **构建通路掩码矩阵:**  基于先验生物网络信息，构建通路掩码矩阵。该矩阵指示哪些基因属于哪些通路，用于在模型训练中引导模型关注通路信息。
  * *可以考虑之前讨论的更高级的掩码策略，例如基于基因功能或网络拓扑结构的动态掩码。*
* **E. 基因编码器 (ScINTEG 原有模块):**

  * **编码基因特征到潜在空间:**  使用神经网络 (例如多层感知机 MLP 或图神经网络 GNN) 将基因特征编码到低维潜在空间。这个潜在空间可以捕获基因的本质特征和通路信息。
  * *保留 ScINTEG 原有的基因编码器模块，但可以根据需要调整网络结构和参数。*
* **F. 自适应网络精简 (灵感来自 scNET):**

  * **动态精简基因网络:**  在基因编码器的基础上，引入自适应网络精简机制。例如，可以使用注意力机制 (Attention Mechanism) 来学习基因之间重要的相互作用，并根据学习到的注意力权重动态地精简基因网络。
  * *借鉴 scNET 的 `reduce_network` 函数的思想，提高模型的效率和可解释性，并关注更重要的基因相互作用。*
* **G. 细胞解码器 (灵感来自 expimap - CVAE 结构):**

  * **解码基因潜在表示到细胞表示:**  引入细胞解码器模块，将基因的潜在表示解码为细胞的表示。这可以使用另一个神经网络 (例如 MLP 或 GNN) 实现。
  * *这是对原始 ScINTEG 的一个重要改进，弥补了之前模型中缺乏细胞解码机制的不足。借鉴 expimap 的 CVAE 结构，可以更好地学习细胞层面的信息。*
* **H. 调控基因模块 (RGM) 识别 (灵感来自 CEFCON):**

  * **识别调控基因模块 (RGMs):**  基于学习到的基因和细胞表示，以及精简后的基因网络，识别调控基因模块 (RGMs)。RGMs 代表一组协同调控细胞状态的基因集合。
  * *借鉴 CEFCON 中 RGM 识别的思想，可以帮助我们理解细胞状态的调控机制，并识别关键的驱动调控因子。*
* **I. 输出: 潜在基因表示, 细胞表示, 精简基因网络, RGMs:**

  * **潜在基因表示:**  基因编码器学习到的基因的低维潜在表示。
  * **细胞表示:**  细胞解码器学习到的细胞的表示。
  * **精简基因网络:**  自适应网络精简模块得到的精简后的基因网络，突出重要的基因相互作用。
  * **调控基因模块 (RGMs):**  模型识别出的调控基因模块，用于后续的生物学解释和分析。
  * *可选的输出还可以包括通路活性评分等，进一步丰富模型的输出信息。*

**总结:**

这个重新设计的 ScINTEG 模型通过 DAG 的形式清晰地展示了模型的各个模块和数据流。它融合了 expimap, scNET 和 CEFCON 的优点，并在原始 ScINTEG 的基础上进行了改进和增强，例如引入了细胞解码器和自适应网络精简机制。

这个模型旨在更全面、更有效地分析单细胞 RNA-seq 数据，并提供更丰富的生物学 insights，例如基因和细胞的潜在表示，精简的基因网络，以及调控基因模块等。

希望这个重新设计的 ScINTEG 模型能够满足您的需求。如果您有任何进一步的问题或需要更详细的解释，请随时提出。

好的，为了形成更详细的设计，我将逐个模块地展开解释重新设计的 ScINTEG 模型，并结合您之前讨论的 expimap, scNET 和 CEFCON 模型的优点。

### 重新设计的 ScINTEG 模型详细设计

**模型概述:**

重新设计的 ScINTEG 模型旨在整合单细胞 RNA-seq 数据和先验生物网络信息，通过深度学习方法学习基因和细胞的潜在表示，并识别调控基因模块 (RGMs)。模型的核心思想是以基因通路为指导，利用自适应网络精简机制关注重要的基因相互作用，并通过引入细胞解码器来更全面地理解细胞状态。

**模型架构 (DAG 再次呈现):**

```mermaid
graph LR
    A[输入: 单细胞 RNA-seq 数据 & 先验生物网络] --> B(数据预处理);
    B --> C{基因特征提取};
    C --> D[通路掩码构建];
    D --> E(基因编码器);
    E --> F{自适应网络精简};
    F --> G(细胞解码器);
    G --> H{调控基因模块 (RGM) 识别};
    H --> I[输出: 潜在基因表示, 细胞表示, 精简基因网络, RGMs];

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style I fill:#ccf,stroke:#333,stroke-width:2px
    style B fill:#eee,stroke:#333,stroke-width:1px
    style C fill:#eee,stroke:#333,stroke-width:1px
    style D fill:#eee,stroke:#333,stroke-width:1px
    style E fill:#eee,stroke:#333,stroke-width:1px
    style F fill:#eee,stroke:#333,stroke-width:1px
    style G fill:#eee,stroke:#333,stroke-width:1px
    style H fill:#eee,stroke:#333,stroke-width:1px
```

**模块详细设计:**

**A. 输入:**

* **单细胞 RNA-seq 数据:**
  * **格式:**  基因表达矩阵，通常为 CSV, TSV, AnnData (.h5ad) 等格式。
  * **内容:**  行代表基因 (gene symbols 或 gene IDs)，列代表细胞 (cell barcodes)。矩阵中的元素是基因在细胞中的表达计数 (UMI counts, TPM, CPM 等)。
  * **来源:**  例如 10x Genomics, Drop-seq, inDrop 等单细胞测序平台的数据。
* **先验生物网络:**
  * **格式:**  邻接矩阵或边列表，可以是文本文件或图数据库格式。
  * **内容:**  描述基因之间的已知相互作用关系，例如基因调控网络、蛋白质相互作用网络、通路数据库 (KEGG, GO, Reactome) 等。
  * **来源:**  公开数据库 (STRING, KEGG, GO, Reactome, MSigDB 等), 文献挖掘结果, 或领域知识构建的网络。

**B. 数据预处理 (借鉴 CEFCON):**

* **质量控制 (QC):**
  * **细胞过滤:**  根据以下指标过滤低质量细胞：
    * **低基因数细胞:**  细胞中检测到的基因数量过少。
    * **高线粒体基因比例细胞:**  线粒体基因表达比例过高，可能表示细胞受损或凋亡。
    * **高核糖体基因比例细胞:**  核糖体基因表达比例异常高。
  * **基因过滤:**  过滤在少量细胞中表达的基因，去除低信息量基因。
* **标准化 (Normalization):**
  * **方法:**  可以选择以下标准化方法：
    * **CPM (Counts Per Million):**  将每个细胞的基因表达计数除以该细胞的总计数，再乘以一百万。
    * **TPM (Transcripts Per Million):**  考虑基因长度的标准化方法，更适合比较不同基因之间的表达水平。
    * **SCTransform:**  基于负二项分布的方差稳定化转换，可以有效去除技术噪声。
    * **Log-normalization:**  对 CPM 或 TPM 数据取对数 (log(x+1))，减小高表达基因的影响。
  * **选择:**  根据数据特点和下游分析需求选择合适的标准化方法。
* **特征选择/基因过滤:**
  * **方法:**
    * **高变基因 (HVGs):**  选择在细胞间表达变异程度高的基因，这些基因通常与细胞类型或状态差异相关。可以使用 Seurat, Scanpy 等工具包中的方法识别 HVGs。
    * **基于生物学知识的基因选择:**  根据先验知识选择与研究问题相关的基因集，例如特定通路或功能相关的基因。
  * **目的:**  降低数据维度，减少噪声，提高计算效率，并聚焦于生物学相关的基因。
* **可选的批次效应校正:**
  * **方法:**  如果数据来自多个批次，可以使用批次效应校正方法，例如：
    * **ComBat:**  经验贝叶斯方法，调整批次间的均值和方差差异。
    * **Harmony:**  基于迭代聚类和对齐的方法，适用于复杂批次效应。
    * **Scanorama:**  基于图匹配的方法，保留数据集特异性。
  * **判断:**  根据数据批次效应的程度和下游分析需求决定是否进行批次效应校正。

**C. 基因特征提取 (灵感来自 scNET):**

* **方法 1: 基于基因表达统计特征:**
  * **特征:**  对于每个基因，计算其在所有细胞中的表达统计特征，例如：
    * **均值表达量:**  基因在所有细胞中的平均表达水平。
    * **变异系数 (CV):**  基因表达水平的变异程度。
    * **离散系数 (Dispersion):**  基因表达分布的离散程度。
    * **细胞特异性指数:**  衡量基因在不同细胞类型中的表达特异性。
  * **优点:**  简单易计算，直接反映基因的表达特征。
  * **缺点:**  忽略了基因之间的相互作用关系。
* **方法 2: 基于图神经网络的特征提取 (更高级):**
  * **构建基因共表达网络:**  基于单细胞 RNA-seq 数据构建基因共表达网络，例如使用 Pearson 相关系数或 Spearman 相关系数计算基因之间的共表达关系。
  * **图神经网络 (GNN):**  使用图神经网络 (例如 GCN, GAT) 在基因共表达网络上学习基因的节点表示。GNN 可以聚合邻居节点的信息，从而捕获基因之间的网络关系。
  * **优点:**  可以有效利用基因之间的网络结构信息，学习更丰富的基因特征表示。
  * **缺点:**  计算复杂度较高，模型训练需要更多资源。
* **选择:**  可以根据计算资源和模型复杂度需求选择合适的方法。方法 2 更能体现 scNET 的思想，利用网络信息增强特征表示。

**D. 通路掩码构建 (ScINTEG 原有优势, 可增强):**

* **通路数据库:**  选择合适的通路数据库，例如 KEGG, GO, Reactome, MSigDB 等。
* **基因-通路映射关系:**  从通路数据库中获取基因与通路的映射关系。
* **通路掩码矩阵:**  构建一个二进制矩阵，行代表基因，列代表通路。如果基因 *i* 属于通路 *j*，则矩阵元素 (i, j) 为 1，否则为 0。
* **高级掩码策略 (可考虑):**
  * **基于基因功能注释的掩码:**  除了通路信息，还可以考虑基因的功能注释 (例如 GO terms, molecular functions) 构建更精细的掩码。
  * **基于网络拓扑结构的动态掩码:**  在模型训练过程中，根据基因在网络中的拓扑位置动态调整掩码权重，例如更关注网络中心基因或关键路径上的基因。
  * **多层级掩码:**  构建多层级的掩码，例如通路层级、子通路层级、基因集层级等，更精细地控制模型对通路信息的利用。

**E. 基因编码器 (ScINTEG 原有模块):**

* **网络结构:**  可以使用以下神经网络结构：
  * **多层感知机 (MLP):**  简单有效，适用于基因特征是独立向量的情况 (方法 1 的基因特征提取)。
  * **图神经网络 (GNN):**  如果基因特征提取使用了 GNN (方法 2)，则基因编码器也可以继续使用 GNN，例如堆叠多层 GCN 或 GAT 层。
* **输入:**  基因特征向量 (来自模块 C)。
* **输出:**  基因的低维潜在表示向量。
* **激活函数:**  可以使用 ReLU, LeakyReLU, ELU 等激活函数。
* **正则化:**  可以使用 Dropout, Batch Normalization 等正则化技术防止过拟合。
* **损失函数:**  通常与下游任务的损失函数结合优化，例如重构损失 (用于自编码器结构) 或分类损失 (用于细胞类型分类任务)。

**F. 自适应网络精简 (灵感来自 scNET):**

* **注意力机制 (Attention Mechanism):**  在基因编码器的输出层引入注意力机制，学习基因之间相互作用的重要性权重。
  * **计算注意力权重:**  可以使用点积注意力 (Dot-Product Attention), 多层感知机注意力 (MLP Attention) 等方法计算基因 *i* 和基因 *j* 之间的注意力权重  *a `<sub>`ij `</sub>`*。
  * **注意力权重矩阵:**  得到一个注意力权重矩阵 *A*，其中元素 *A `<sub>`ij `</sub>`* = *a `<sub>`ij `</sub>`* 表示基因 *i* 和基因 *j* 之间的相互作用强度。
* **网络精简:**  根据注意力权重矩阵 *A* 精简基因网络。
  * **阈值方法:**  设定一个阈值 *θ*，保留注意力权重 *a `<sub>`ij `</sub>`* 大于阈值 *θ* 的基因相互作用边。
  * **Top-K 方法:**  对于每个基因 *i*，保留与其注意力权重最高的 *K* 个基因的相互作用边。
  * **稀疏化正则化:**  在损失函数中加入稀疏化正则项 (例如 L1 正则化) 约束注意力权重矩阵 *A* 的稀疏性，鼓励模型学习稀疏的基因网络。
* **目的:**  提取重要的基因相互作用，提高模型的可解释性和效率，并关注关键的调控关系。借鉴 scNET 的 `reduce_network` 函数的思想。

**G. 细胞解码器 (灵感来自 expimap - CVAE 结构):**

* **网络结构:**  可以使用以下神经网络结构：
  * **多层感知机 (MLP):**  将基因的潜在表示解码为细胞的表示。
  * **图神经网络 (GNN):**  如果需要考虑细胞之间的相互作用关系 (例如细胞通讯网络)，可以使用 GNN 作为细胞解码器。
* **输入:**  基因编码器的输出 (基因的潜在表示)。
* **输出:**  细胞的表示向量。
* **CVAE 结构 (可选, 借鉴 expimap):**  可以借鉴 expimap 的 CVAE 结构，将细胞解码器构建为变分自编码器 (VAE) 的解码器部分。
  * **编码器 (细胞编码器):**  将细胞的基因表达数据编码为细胞的潜在表示 (均值和方差)。
  * **解码器 (基因解码器):**  将细胞的潜在表示解码回基因表达空间，用于重构基因表达数据。
  * **损失函数:**  包括重构损失 (例如均方误差 MSE) 和 KL 散度损失，用于优化 VAE 模型。
  * **优点:**  可以学习细胞的概率潜在表示，并用于生成新的细胞数据或进行细胞聚类分析。
* **目的:**  学习细胞层面的信息，弥补原始 ScINTEG 缺乏细胞解码机制的不足。

**H. 调控基因模块 (RGM) 识别 (灵感来自 CEFCON):**

* **方法 1: 基于基因和细胞表示的聚类分析:**
  * **聚类算法:**  使用聚类算法 (例如 k-means, Louvain, Leiden) 对基因的潜在表示或细胞的表示进行聚类。
  * **RGM 定义:**  将聚类得到的基因簇定义为调控基因模块 (RGMs)。每个 RGM 代表一组协同调控细胞状态的基因集合。
  * **优点:**  简单直接，易于实现。
  * **缺点:**  聚类结果可能不稳定，对聚类算法和参数选择敏感。
* **方法 2: 基于网络模块检测算法 (更高级, 借鉴 CEFCON):**
  * **网络模块检测算法:**  使用网络模块检测算法 (例如 Louvain, Infomap, Walktrap) 在精简后的基因网络上检测模块结构。
  * **RGM 定义:**  将网络模块检测算法得到的基因模块定义为调控基因模块 (RGMs)。
  * **优点:**  可以利用基因网络结构信息，识别更具有生物学意义的 RGMs。
  * **缺点:**  网络模块检测算法的参数选择也会影响结果。
* **功能富集分析:**  对识别出的 RGMs 进行功能富集分析 (例如 GO 富集分析, KEGG 通路富集分析)，验证 RGMs 的生物学意义，并理解其功能。

**I. 输出:**

* **潜在基因表示:**  基因编码器学习到的基因的低维潜在表示向量，可以用于基因功能预测、基因相似性分析等下游分析。
* **细胞表示:**  细胞解码器学习到的细胞的表示向量，可以用于细胞聚类、细胞类型识别、细胞轨迹推断等下游分析。
* **精简基因网络:**  自适应网络精简模块得到的精简后的基因网络，可以用于基因网络分析、关键基因识别、调控关系推断等。
* **调控基因模块 (RGMs):**  模型识别出的调控基因模块，可以用于理解细胞状态的调控机制、识别关键的驱动调控因子、药物靶点发现等。
* **可选输出:**
  * **通路活性评分:**  基于基因的潜在表示和通路掩码，计算每个细胞在不同通路上的活性评分。
  * **基因重要性排序:**  根据注意力权重或模型参数，对基因的重要性进行排序。

**总结:**

这个详细设计方案深入解释了重新设计的 ScINTEG 模型的各个模块，并参考了 expimap, scNET 和 CEFCON 模型的优点。它提供了更具体的算法和方法选择，并强调了每个模块的设计目的和生物学意义。

希望这个更详细的设计能够满足您的需求。如果您有任何进一步的问题，例如关于特定模块的具体实现细节、代码示例、或模型训练策略等，请随时提出，我会尽力解答。
