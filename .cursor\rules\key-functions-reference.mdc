---
description: 
globs: 
alwaysApply: false
---
# Key Functions & Classes Reference

## CEFCON Key Functions

### Core Training Pipeline
- **Main Entry**: `main()` in [CEFCON.py](mdc:multi_model/cefcon/CEFCON.py) - CLI interface for full pipeline
- **Data Preparation**: `data_preparation()` in [utils.py](mdc:multi_model/cefcon/utils.py) - Input processing and network construction
- **Model Training**: `NetModel.run()` in [cell_lineage_GRN.py](mdc:multi_model/cefcon/cell_lineage_GRN.py) - Main training loop
- **Network Inference**: `NetModel.get_network()` - Generate predicted GRN from attention weights
- **Driver Analysis**: `driver_regulators()` in [driver_regulators.py](mdc:multi_model/cefcon/driver_regulators.py) - MDS/MFVS control analysis

### Key Classes
- **`NetModel`**: Main model class with training orchestration
- **`GraphAttention_layer`**: Custom attention mechanism for gene interactions  
- **`GRN_Encoder`**: Graph neural network encoder with multi-head attention
- **`CefconResults`**: Results container with analysis methods

## expiMap Key Functions

### Model Interface
- **Main Class**: `EXPIMAP` in [expimap_model.py](mdc:multi_model/expimap/expimap_model.py) - High-level model wrapper
- **Core Model**: `expiMap` in [expimap.py](mdc:multi_model/expimap/expimap.py) - Neural network implementation
- **Training**: `EXPIMAP.train()` - Training pipeline with regularization
- **Latent Analysis**: `EXPIMAP.get_latent()` - Extract latent representations
- **Interpretation**: `EXPIMAP.latent_directions()` - Pathway direction analysis

### Key Components
- **`MaskedLinear`** in [modules.py](mdc:multi_model/expimap/modules.py) - Pathway-guided masking
- **`expiMapTrainer`** in [regularized.py](mdc:multi_model/expimap/regularized.py) - Training with group lasso
- **`ProxGroupLasso`** - Proximal operator for sparse regularization
- **`CVAELatentsMixin`** - Base VAE functionality

## scGNN Key Functions

### Main Training Pipeline
- **Entry Point**: `main()` in [scGNN.py](mdc:multi_model/scGNN/scGNN.py) - Training loop with multiple data types
- **Preprocessing**: `preprocessing10X()` in [PreprocessingscGNN.py](mdc:multi_model/scGNN/PreprocessingscGNN.py) - 10X data processing
- **Graph Construction**: `generateAdj()` in [graph_function.py](mdc:multi_model/scGNN/graph_function.py) - KNN graph generation
- **Evaluation**: `test_clustering_results()` in [benchmark_util.py](mdc:multi_model/scGNN/benchmark_util.py) - Clustering metrics

### Key Components
- **`VAE`** in [model.py](mdc:multi_model/scGNN/model.py) - Variational autoencoder
- **`scDataset`** in [util_function.py](mdc:multi_model/scGNN/util_function.py) - Data loading utilities
- **Distance Calculations**: `calculateKNNgraphDistanceMatrix()` family in [graph_function.py](mdc:multi_model/scGNN/graph_function.py)
- **GAE Integration**: [gae/train.py](mdc:multi_model/scGNN/gae/train.py) - Graph autoencoder training

## scNET Key Functions

### Main Interface
- **Entry Point**: `run_scNET()` in [main.py](mdc:multi_model/scNET/main.py) - Complete pipeline execution
- **Training**: `train()` in [main.py](mdc:multi_model/scNET/main.py) - Model training with batching
- **Preprocessing**: `pre_processing()` - Data preparation and graph construction
- **Network Building**: `build_network()` - Integration with prior knowledge networks

### Key Models
- **`scNET`** in [MultyGraphModel.py](mdc:multi_model/scNET/MultyGraphModel.py) - Main multi-graph model
- **`MutualEncoder`** - Handles KNN and PPI graph encoding
- **`DimEncoder`** - Dimension reduction with transformer convolution
- **`FeatureDecoder`** - Feature reconstruction decoder

### Analysis Functions
- **`build_co_embeded_network()`** in [coEmbeddedNetwork.py](mdc:multi_model/scNET/coEmbeddedNetwork.py) - Network construction from embeddings
- **`test_KEGG_prediction()`** - Pathway prediction evaluation
- **`find_downstream_tfs()`** - Transcription factor analysis

## Common Utility Functions

### Data Loading Patterns
- **CSV Reading**: `loadscExpression()` in scGNN, `read_csv()` patterns in CEFCON
- **AnnData Processing**: `sc.read()` family functions across tools
- **Graph Construction**: KNN graph building in scGNN and scNET

### Evaluation Metrics
- **Clustering**: ARI, NMI, Silhouette scores in [benchmark_util.py](mdc:multi_model/scGNN/benchmark_util.py)
- **Network Evaluation**: AUPRC, AUROC in [eval_utils.py](mdc:multi_model/cefcon/eval_utils.py)
- **Imputation**: Error metrics in scGNN benchmark utilities

### Visualization Functions
- **UMAP Plotting**: `drawUMAP()` in [benchmark_util.py](mdc:multi_model/scGNN/benchmark_util.py)
- **Network Visualization**: `plot_network()` in [cefcon_result_object.py](mdc:multi_model/cefcon/cefcon_result_object.py)
- **Heatmaps**: `plot_RGM_activity_heatmap()` in CEFCON results

## Integration Targets

### High-Value Functions for Reuse
- **Graph Construction**: Merge scGNN and scNET approaches
- **Attention Mechanisms**: Combine CEFCON and scNET attention layers
- **Regularization**: Adapt expiMap group lasso for other models
- **Evaluation**: Standardize metrics across all tools

### API Design Considerations
- Consistent parameter naming across tools
- Unified data input/output formats
- Standardized training interfaces
- Common evaluation and visualization functions
