# CEFCON模型的有向无环图(DAG)解读

```
[基因表达数据] ─┐
                │
[先验网络数据] ─┼─→ [数据预处理] ─→ [图神经网络] ─→ [网络推断] ─→ [驱动调控因子] ─→ [RGM活性分析]
                │                      │
[差异表达数据] ─┘                [基因嵌入空间]
```

## 一、数据预处理阶段

```
[基因表达数据] ───────┐
                      ↓
[先验网络数据] ─→ [筛选共有基因] ─→ [转录因子标注] ─→ [网络中心性计算]
                      ↑                                     ↓
[可选:差异表达] ──────┘                         [相关性额外边添加] ─→ [整合数据]
```

- **输入处理**:

  - 基因表达矩阵: 细胞×基因
  - 先验网络: `from`→`to`格式的边列表
  - 差异表达数据: 基因差异表达分数
- **数据整合**:

  - 筛选同时存在于表达数据和先验网络的基因
  - 添加转录因子标注(`is_TF`)
  - 计算网络中心性(入度/出度中心性)
  - 基于Spearman相关性添加额外高相关边

## 二、图神经网络阶段

```
[整合数据] ─→ [构建PyG数据] ─→ [GRN编码器] ─→ [多头注意力层1] ───┐
                                   ↓             ↓                │
                            [负样本生成] ─→ [DeepGraphInfomax] ←──┼─── [多头注意力层2]
                                               ↓                  │
                                        [注意力权重提取] ←─────────┘
```

- **网络架构**:

  - `GRN_Encoder`: 包含多头图注意力层
  - 注意力类型: COS(余弦)、AD(加性)、SD(缩放点积)
  - 使用 `DeepGraphInfomax`进行自监督学习
  - 输入特征: 基因表达向量
  - 输出: 基因嵌入和边注意力权重
- **训练过程**:

  - 重复多次训练，获取稳定的注意力权重
  - 合并不同层注意力权重: `att_weights = self.miu * weights_first[1] + (1 - self.miu) * weights_second[1]`
  - 边权重用于量化基因间调控强度

## 三、网络推断阶段

```
[注意力权重] ─→ [有向边过滤] ─→ [边权重缩放] ─→ [设置阈值] ─→ [细胞谱系GRN]
     │                                                              │
     └───────────────────────→ [基因嵌入向量] ─────────────────────→┘
```

- **网络构建**:
  - 根据注意力权重选择最重要的边
  - 缩放边权重: `att_coef_i * degree_i`
  - 应用阈值选择顶部N个边: `filtered_edge_idx = np.argsort(-att_weights_combined)[0:g.number_of_nodes() * edge_threshold_avgDegree]`
  - 构建最终细胞谱系特异性的GRN

## 四、驱动调控因子识别

```
[细胞谱系GRN] ─→ [基因影响分数] ─→ [网络控制理论] ─→ [MDS控制] ──┐
                       │                              │          │
                       │                        [MFVS控制] ─────→ [驱动调控因子]
                       │                                            ↑
                       └────────────→ [基因打分排序] ───────────────┘
```

- **驱动因子分析**:
  - 计算基因影响分数: `influence_score = lam * score_out + (1 - lam) * score_in`
  - 使用网络控制理论(MDS和MFVS)识别关键控制节点
  - MDS: 最小支配集(Minimum Dominating Set)
  - MFVS: 最小反馈顶点集(Minimum Feedback Vertex Set)
  - 根据影响分数和控制理论结果综合识别驱动调控因子

## 五、RGM活性分析

```
[驱动调控因子] ─→ [定义RGM] ─→ [基因集活性计算] ─→ [细胞分群] ─→ [功能解析]
                     │                │
                     ↓                ↓
              [前馈调控模块]      [AUCell评分]
```

- **RGM分析**:
  - RGM(Regulon-like Gene Module): 类似调控子的基因模块
  - 将驱动调控因子及其目标基因定义为RGM
  - 使用AUCell计算每个细胞中RGM的活性
  - 基于RGM活性进行细胞聚类和功能解析

## 六、整体数据流图

```
[数据输入] → [预处理] → [图神经网络] → [网络推断] → [基因影响分析] → [驱动因子识别] → [RGM活性评估] → [生物学解释]
```

CEFCON的核心创新在于将图神经网络与网络控制理论结合，使用注意力机制学习基因间调控关系，然后通过影响分数和网络控制分析识别关键驱动调控因子，最终构建细胞谱系特异性的基因调控网络。

# 一、CEFCON数据预处理阶段详细解读

## 输入数据解析

```
[基因表达数据] ──→ [格式检查] ──→ [转换为AnnData] ──┐
                                               ↓
[先验网络数据] ──→ [格式检查] ──→ [转换为DataFrame] ───→ [基因匹配筛选]
                                                      ↑
[差异表达数据] ──→ [格式检查] ──→ [转换为Series/DataFrame] ┘
```

### 1. 基因表达数据处理

```python
if isinstance(input_expData, str):  # 文件路径输入
    p = Path(input_expData)
    if p.suffix == '.csv':
        adata = sc.read_csv(input_expData, first_column_names=True)  # CSV格式
    else:
        adata = sc.read_h5ad(input_expData)  # H5AD格式
elif isinstance(input_expData, sc.AnnData):  # 已有AnnData对象
    adata = input_expData
    lineages = adata.uns.get('lineages')  # 获取谱系信息
elif isinstance(input_expData, pd.DataFrame):  # DataFrame格式
    adata = sc.AnnData(X=input_expData)  # 转换为AnnData

# 基因名统一为大写处理
adata.var_names = adata.var_names.str.upper()
```

- **技术细节**：
  - 支持多种输入格式：CSV文件、H5AD文件、AnnData对象、DataFrame
  - 自动检测物种类型(人类或小鼠)：`possible_species = 'mouse' if bool(re.search('[a-z]', adata.var_names[0])) else 'human'`
  - 统一基因名为大写，以便后续匹配

### 2. 先验网络数据处理

```python
if isinstance(input_priorNet, str):  # 文件路径输入
    netData = pd.read_csv(input_priorNet, index_col=None, header=0)
elif isinstance(input_priorNet, pd.DataFrame):  # 已有DataFrame
    netData = input_priorNet.copy()

# 基因名统一为大写
netData['from'] = netData['from'].str.upper()
netData['to'] = netData['to'].str.upper()

# 过滤边：只保留表达数据中存在的基因
netData = netData.loc[netData['from'].isin(adata.var_names.values) & 
                      netData['to'].isin(adata.var_names.values), :]

# 去除重复边
netData = netData.drop_duplicates(subset=['from', 'to'], keep='first', inplace=False)
```

- **技术细节**：
  - 先验网络结构: 源节点(`from`) → 目标节点(`to`)的边列表
  - 进行基因名匹配：只保留同时存在于表达数据和先验网络中的基因
  - 去除重复边：确保网络边唯一

## 网络特征计算

```
[过滤后网络数据] ──→ [转换为NetworkX] ──→ [计算入度中心性] ──┐
                                           ↓              │
                                   [计算出度中心性] ──────→ [合并中心性指标]
```

```python
# 转换为NetworkX有向图对象
priori_network = nx.from_pandas_edgelist(netData, source='from', target='to', create_using=nx.DiGraph)
priori_network_nodes = np.array(priori_network.nodes())

# 计算网络中心性指标
in_degree = pd.DataFrame.from_dict(nx.in_degree_centrality(priori_network),
                                   orient='index', columns=['in_degree'])
out_degree = pd.DataFrame.from_dict(nx.out_degree_centrality(priori_network),
                                    orient='index', columns=['out_degree'])
centrality = pd.concat([in_degree, out_degree], axis=1)
centrality = centrality.loc[priori_network_nodes, :]
```

- **技术细节**：
  - 使用NetworkX库建立有向图
  - 计算两种中心性指标：
    - **入度中心性**：节点有多少入边，归一化为 `入边数/(N-1)`，其中N是节点总数
    - **出度中心性**：节点有多少出边，归一化为 `出边数/(N-1)`
  - 这些中心性指标衡量基因在网络中的拓扑重要性

## 节点索引和基因名映射

```
[网络节点] ──→ [创建索引-基因映射] ──→ [转换边列表索引] ──→ [边列表]
```

```python
# 创建节点索引与基因名的映射关系
idx_GeneName_map = pd.DataFrame({'idx': range(len(priori_network_nodes)),
                                 'geneName': priori_network_nodes},
                                index=priori_network_nodes)

# 将边列表从基因名转换为索引表示
edgelist = pd.DataFrame({'from': idx_GeneName_map.loc[netData['from'].tolist(), 'idx'].tolist(),
                         'to': idx_GeneName_map.loc[netData['to'].tolist(), 'idx'].tolist()})
```

- **技术细节**：
  - 为每个基因分配唯一索引
  - 将边列表从基因名格式转换为索引格式
  - 这种转换便于后续在图神经网络中处理

## 转录因子识别和注释

```
[网络节点] ──→ [加载TF列表] ──→ [标记TF节点] ──→ [TF注释]
```

```python
# 初始化所有节点为转录因子
is_TF = np.ones(len(priori_network_nodes), dtype=int)

# 加载对应物种的转录因子列表
if possible_species == 'human':
    TFs_df = TFs_human
else:
    TFs_df = TFs_mouse
TF_list = TFs_df.iloc[:, 0].str.upper()

# 标记非转录因子节点
is_TF[~np.isin(priori_network_nodes, TF_list)] = 0
```

- **技术细节**：
  - 从预定义的转录因子列表(TFs_human或TFs_mouse)识别转录因子
  - 用二值标记(0/1)标注每个基因是否为转录因子
  - 这一信息在后续识别调控关系时很重要

## 数据过滤和谱系划分

```
[表达数据] ──→ [过滤保留网络基因] ──→ [谱系划分] ──→ [创建谱系特异AnnData]
```

```python
# 只保留网络中存在的基因
adata = adata[:, priori_network_nodes]

# 谱系划分
if lineages is None:
    cells_in_lineage_dict = {'all': adata.obs_names}  # 如无谱系信息，将所有细胞视为一个谱系
else:
    cells_in_lineage_dict = {l: adata.obs_names[adata.obs[l].notna()] for l in lineages}
```

- **技术细节**：
  - 表达数据过滤：只保留网络中存在的基因
  - 谱系划分：根据AnnData中的谱系标注划分细胞
  - 如无谱系信息，将所有细胞视为一个谱系"all"

## 相关性边添加

```
[谱系表达数据] ──→ [计算Spearman相关性] ──→ [筛选高相关边(>0.6)] ──→ [合并原始边]
                                                      |
                                          [按相关性排序取前N%] ──────┘
```

```python
if additional_edges_pct > 0:
    # 提取表达矩阵
    if isinstance(adata_l.X, sparse.csr_matrix):
        gene_exp = pd.DataFrame(adata_l.X.A.T, index=priori_network_nodes)
    else:
        gene_exp = pd.DataFrame(adata_l.X.T, index=priori_network_nodes)
  
    ori_edgeNum = len(edgelist)
  
    # 计算Spearman相关性矩阵
    SCC, _ = stats.spearmanr(gene_exp, axis=1)
    edges_corr = np.absolute(SCC)
    np.fill_diagonal(edges_corr, 0.0)  # 移除自环
  
    # 筛选高相关边(>0.6)
    x, y = np.where(edges_corr > 0.6)
    addi_top_edges = pd.DataFrame({'from': x, 'to': y, 'weight': edges_corr[x, y]})
  
    # 限制额外边数量
    addi_top_k = int(gene_exp.shape[0] * (gene_exp.shape[0] - 1) * additional_edges_pct)
    if len(addi_top_edges) > addi_top_k:
        addi_top_edges = addi_top_edges.sort_values(by=['weight'], ascending=False)
        addi_top_edges = addi_top_edges.iloc[0:addi_top_k, 0:2]
  
    # 合并原始边与新增边
    edgelist = pd.concat([edgelist, addi_top_edges.iloc[:, 0:2]], ignore_index=True)
    edgelist = edgelist.drop_duplicates(subset=['from', 'to'], keep='first', inplace=False)
```

- **技术细节**：
  - 计算所有基因对之间的Spearman相关系数
  - 筛选高相关性(>0.6)的基因对作为潜在调控关系
  - 根据用户指定参数 `additional_edges_pct`(默认0.01，即1%)限制添加的边数
  - 高相关边根据相关性强度排序，选择前N个添加
  - 合并原始网络边与新增边，去除重复

## 差异表达整合

```
[谱系特异AnnData] ──→ [加载差异表达数据] ──→ [基因匹配] ──→ [创建节点辅助分数]
```

```python
# 获取差异表达数据
logFC = adata.var.get(l + '_logFC')
if (genes_DE is None) and (logFC is None):
    pass  # 无差异表达数据
else:
    if logFC is not None:
        genes_DE = logFC
    else:  # genes_DE是外部输入的DataFrame
        genes_DE = genes_DE.loc[:, li]
  
    # 处理差异表达数据
    genes_DE = pd.DataFrame(genes_DE).iloc[:, 0]
    genes_DE.index = genes_DE.index.str.upper()
    genes_DE = genes_DE[genes_DE.index.isin(priori_network_nodes)].abs().dropna()
  
    # 创建节点辅助分数
    node_score_auxiliary = pd.Series(np.zeros(len(priori_network_nodes)), index=priori_network_nodes)
    node_score_auxiliary[genes_DE.index] = genes_DE.values
    node_score_auxiliary = np.array(node_score_auxiliary)
  
    # 添加到AnnData对象
    adata_l.var['node_score_auxiliary'] = node_score_auxiliary
```

- **技术细节**：
  - 支持两种差异表达数据来源：
    1. AnnData对象的var属性中的 `l + '_logFC'`列(l为谱系名称)
    2. 用户提供的外部差异表达数据
  - 对差异表达数据进行处理：大写基因名、筛选存在于网络中的基因、取绝对值
  - 创建节点辅助分数：将差异表达值映射到网络节点
  - 这些分数在后续图注意力网络中增强差异表达基因的重要性

## 汇总整合

```
[谱系特异AnnData] ──→ [添加TF标记] ──→ [添加中心性指标] ──→ [添加基因索引映射] ──→ [添加边列表] ──→ [添加差异表达]
```

```python
# 为每个谱系创建独立的AnnData对象
adata_l = sc.AnnData(X=adata[c, :].to_df())

# 添加各种注释
adata_l.var['is_TF'] = is_TF  # 转录因子标记
adata_l.varm['centrality_prior_net'] = centrality  # 网络中心性指标
adata_l.varm['idx_GeneName_map'] = idx_GeneName_map  # 基因索引映射
adata_l.uns['name'] = l  # 谱系名称
adata_l.uns['edgelist'] = edgelist  # 边列表
```

- **技术细节**：
  - 为每个谱系创建独立的AnnData对象
  - 在AnnData对象中存储各种预处理信息：
    - `var['is_TF']`: 基因是否为转录因子
    - `varm['centrality_prior_net']`: 网络中心性指标
    - `varm['idx_GeneName_map']`: 基因索引映射
    - `uns['name']`: 谱系名称
    - `uns['edgelist']`: 边列表
    - `var['node_score_auxiliary']`: 差异表达分数(如有)
  - 这个综合注释的AnnData对象将作为后续图神经网络的输入

## 预处理阶段的意义

1. **数据整合**：将离散的基因表达、网络、差异表达数据整合到统一框架
2. **特征增强**：通过网络中心性、转录因子标记、差异表达等多维特征丰富基因描述
3. **网络优化**：筛选共有基因、去除冗余连接、添加高相关边以平衡先验知识与数据驱动发现
4. **数据结构化**：将所有信息组织成适合图神经网络的格式(节点特征、边列表)

这一阶段为CEFCON后续的深度学习和网络分析奠定了坚实基础，通过整合多源数据，它能够更准确地捕获细胞谱系特异的基因调控模式。

# 二、CEFCON图神经网络阶段详细解读

## 整体架构

```
[AnnData] ──→ [PyG数据转换] ──┬──→ [GRN编码器] ──→ [图注意力层1] ──┐
                              │          ↑              ↓          │
                              │          │        [图注意力层2] ────┤
                              │          │              ↓          │
                              │    [损失函数]   [多头注意力聚合] ────┤
                              │          ↑              ↓          │
                              │          │    [输出基因嵌入向量] ───┤
                              │          │                         │
                              └──→ [负样本生成] ←──── [DeepGraphInfomax] ←┘
```

## 1. PyG数据转换

```python
def __get_PYG_data(self, adata: AnnData) -> Data:
    # 从AnnData中提取边索引
    source_nodes = adata.uns['edgelist']['from'].tolist()
    target_nodes = adata.uns['edgelist']['to'].tolist()
    edge_index = torch.tensor([source_nodes, target_nodes], dtype=torch.long)

    # 节点特征：基因表达数据
    x = torch.from_numpy(adata.to_df().T.to_numpy())
    pyg_data = Data(x=x, edge_index=edge_index)

    # 节点辅助分数(差异表达)
    if 'node_score_auxiliary' in adata.var:
        pyg_data.node_score_auxiliary = torch.tensor(adata.var['node_score_auxiliary'].to_numpy(),
                                                    dtype=torch.float32).view(-1, 1)
    else:
        print('  Warning: Auxiliary gene scores (e.g., differential expression level) are not considered!')

    self._idx_GeneName_map = adata.varm['idx_GeneName_map']
    self._adata = adata

    return pyg_data
```

- **功能**：将AnnData转换为PyTorch Geometric库的Data对象
- **技术细节**：
  - **节点特征(x)**：基因表达向量，维度为[n_genes, n_cells]
  - **边索引(edge_index)**：从预处理存储的边列表获取，格式为[2, n_edges]
  - **节点辅助分数**：如存在差异表达数据，添加为节点属性
  - **数据存储**：保存基因映射和原始AnnData以便后续处理

## 2. 图注意力层实现

```python
class GraphAttention_layer(MessagePassing):
    def __init__(self,
                input_dim: int,
                output_dim: int,
                attention_type: str = 'COS',
                flow: str = 'source_to_target',
                heads: int = 1,
                concat: bool = True,
                dropout: float = 0.0,
                add_self_loops: bool = True,
                to_undirected: bool = False,
                **kwargs):
        # 初始化...

    def forward(self, x: Tensor, edge_index: Adj, x_auxiliary: Tensor,
                return_attention_weights: Optional[bool] = None):
        # 线性变换
        x_l = self.lin_l(x).view(-1, H, C)
        x_r = self.lin_r(x).view(-1, H, C)
  
        # 根据注意力类型选择处理方式
        if self.attention_type == 'AD':  # 加性注意力
            out = self.propagate(edge_index, x=(x_l, x_r), x_norm=None,
                                x_auxiliary=x_auxiliary, size=None)
        elif self.attention_type == 'COS':  # 余弦相似度注意力
            x_norm_l = F.normalize(x_l, p=2., dim=-1)
            x_norm_r = F.normalize(x_r, p=2., dim=-1)
            out = self.propagate(edge_index, x=(x_l, x_r), x_norm=(x_norm_l, x_norm_r),
                                x_auxiliary=x_auxiliary, size=None)
        else:  # SD (缩放点积注意力)
            out = self.propagate(edge_index, x=(x_l, x_r), x_norm=None,
                                x_auxiliary=x_auxiliary, size=None)
  
        # 返回结果和注意力权重
        # ...
  
    def message(self, edge_index_i: Tensor, x_i: Tensor, x_j: Tensor,
                x_norm_i: Optional[Tensor], x_norm_j: Optional[Tensor],
                x_auxiliary_j: Tensor, size_i: Optional[int]):
        Tau = 1.0  # 温度超参数
        if self.attention_type == 'AD':  # 加性注意力
            alpha = (x_j * self.att_l).sum(-1) + (x_i * self.att_r).sum(-1)
            alpha = x_auxiliary_j * F.leaky_relu(alpha, 0.2)
        elif self.attention_type == 'COS':  # 余弦相似度注意力
            alpha = x_auxiliary_j * torch.abs((x_norm_i * x_norm_j).sum(dim=-1))
            Tau = 0.25
        else:  # 'SD'缩放点积注意力
            alpha = x_auxiliary_j * torch.abs((x_i * x_j).sum(dim=-1)) / math.sqrt(self.output_dim)

        # softmax归一化
        alpha = softmax(alpha / Tau, edge_index_i, num_nodes=size_i)
        self._alpha = alpha
        alpha = F.dropout(alpha, p=self.dropout, training=self.training)
        return x_j * alpha.view(-1, self.heads, 1)
```

- **功能**：自定义图注意力层，处理节点间消息传递
- **技术细节**：
  - **支持三种注意力机制**：
    - **AD(加性注意力)**：类似于GAT，使用可学习向量计算注意力分数
    - **COS(余弦相似度)**：使用节点表示的余弦相似度作为注意力分数
    - **SD(缩放点积)**：使用缩放点积计算注意力分数
  - **注意力计算流程**：
    1. 将节点特征经过线性变换
    2. 根据注意力类型计算原始分数
    3. 乘以节点辅助分数(差异表达)增强重要基因影响
    4. 使用softmax归一化获得最终注意力权重
  - **多头机制**：每个头独立计算注意力，然后合并
  - **消息传递**：使用注意力权重对邻居节点特征加权求和

## 3. GRN编码器架构

```python
class GRN_Encoder(nn.Module):
    def __init__(self,
                input_dim: int,
                hidden_dim: int,
                output_dim: int,
                heads_num: int = 1,
                dropout: float = 0.0,
                attention_type: str = 'COS'):
        super(GRN_Encoder, self).__init__()
        # 初始化...
  
        self.x_input = nn.Linear(input_dim, hidden_dim)
        self.act = nn.GELU()
  
        # 创建两层图注意力
        self.layers = nn.ModuleList([])
        dims = [hidden_dim, hidden_dim]  # 2层
        for l in range(len(dims)):
            concat = True
            last_dim = hidden_dim if l < len(dims) - 1 else output_dim
            self.layers.append(nn.ModuleList([
                BatchNorm(dims[l]),
                # 入向注意力层
                GraphAttention_layer(dims[l], dims[l], heads=heads_num,
                                     concat=concat, dropout=dropout,
                                     attention_type=attention_type),
                # 出向注意力层
                GraphAttention_layer(dims[l], dims[l], heads=heads_num,
                                     concat=concat, dropout=dropout,
                                     attention_type=attention_type,
                                     flow='target_to_source'),
                # 组合层
                nn.Sequential(
                    nn.Linear(dims[l] * 2, hidden_dim),
                    nn.GELU(),
                    nn.Linear(hidden_dim, last_dim),
                    nn.GELU(),
                ),
            ]))

    def forward(self, data):
        # 初始特征转换
        x, edge_index = data.x, data.edge_index
        x_auxiliary = data.node_score_auxiliary if hasattr(data, 'node_score_auxiliary') else torch.ones(x.size(0), 1)
  
        x = self.x_input(x)
        x = self.act(x)

        # 两层图注意力处理
        for l, (norm, gal_in, gal_out, combine) in enumerate(self.layers):
            x_in = norm(x)
    
            # 入向和出向注意力
            x_in_in, (_, alpha_in) = gal_in(x_in, edge_index, x_auxiliary, return_attention_weights=True)
            x_in_out, (_, alpha_out) = gal_out(x_in, edge_index, x_auxiliary, return_attention_weights=True)
    
            # 组合入向和出向信息
            x_combine = torch.cat([x_in_in, x_in_out], dim=1)
            x_combine = combine(x_combine)
    
            # 残差连接
            x = x_combine + x
    
            # 保存注意力权重
            if l == 0:
                self.att_weights_first = (edge_index, alpha_in, alpha_out)
            else:
                self.att_weights_second = (edge_index, alpha_in, alpha_out)
  
        # 输出
        self.x_embs = x
        return x
```

- **功能**：GRN编码器模型，使用双向图注意力处理基因交互关系
- **技术细节**：
  - **层次结构**：
    1. 输入特征线性变换
    2. 两层图注意力处理，每层包含：
       - 批归一化
       - 入向图注意力(接收信息)
       - 出向图注意力(发送信息)
       - 组合层(合并入向和出向表示)
    3. 最终输出基因嵌入向量
  - **双向注意力**：同时考虑基因作为调控者和被调控者的角色
  - **残差连接**：促进梯度流动和特征保留
  - **注意力权重存储**：保存各层注意力权重用于后续网络推断

## 4. 自监督学习架构

```python
def __corruption(data: Data) -> Data:
    # 通过随机打乱节点特征创建负样本
    x, edge_index = data['x'], data['edge_index']
    data_neg = Data(x=x[torch.randperm(x.size(0))], edge_index=edge_index)
    if 'node_score_auxiliary' in data:
        node_score_auxiliary = data['node_score_auxiliary']
        data_neg.node_score_auxiliary = node_score_auxiliary[torch.randperm(node_score_auxiliary.size(0))]
    return data_neg

def __summary(z, *args, **kwargs) -> torch.Tensor:
    # 创建图级表示
    return torch.sigmoid(torch.cat((3 * z.mean(dim=0).unsqueeze(0),
                                   z.max(dim=0)[0].unsqueeze(0),
                                   z.min(dim=0)[0].unsqueeze(0),
                                   2 * z.median(dim=0)[0].unsqueeze(0),
                                   ), dim=0))
```

- **功能**：实现DeepGraphInfomax自监督学习框架
- **技术细节**：
  - **负样本生成**：通过随机打乱节点特征创建负样本，保持原图结构
  - **图级表示**：使用节点嵌入的统计信息(均值、最大值、最小值、中位数)创建图级表示
  - **对比学习**：训练编码器区分原始样本和负样本

## 5. 模型训练流程

```python
def run(self, adata: AnnData, showProgressBar: bool = True):
    # 配置设备
    if self.cuda == -1:
        device = "cpu"
    else:
        device = 'cuda:%s' % self.cuda

    # 准备PyG数据
    data = self.__get_PYG_data(adata).to(device)
    input_dim = data.num_node_features

    # 多次运行取平均
    att_weights_all = []
    emb_out_avg = 0
    for rep in range(self.repeats):
        # 创建编码器和对比学习模型
        encoder = GRN_Encoder(input_dim, self.hidden_dim, self.output_dim, self.heads,
                             dropout=self.dropout, attention_type=self.attention_type).to(device)
        DGI_model = DeepGraphInfomax(hidden_channels=self.output_dim * 4,
                                     encoder=encoder,
                                     summary=self.__summary,
                                     corruption=self.__corruption).to(device)
        optimizer = torch.optim.Adam(DGI_model.parameters(), lr=1e-4, weight_decay=5e-4)

        # 训练循环
        best_encoder = encoder.state_dict()
        min_loss = np.inf
        for epoch in trange(self.epochs):
            loss = self.__train(data, DGI_model, optimizer)
            if min_loss > loss:
                min_loss = loss
                best_encoder = encoder.state_dict()

        # 使用最佳模型获取结果
        encoder.load_state_dict(best_encoder)
        gene_emb, weights_first, weights_second, emb_last = self.__get_encoder_results(data, encoder)
        gene_emb = gene_emb.cpu().detach().numpy()

        # 处理注意力权重
        weights_first = (weights_first[0].cpu().detach(),
                         torch.cat((weights_first[1].mean(dim=1, keepdim=True),
                                   weights_first[2].mean(dim=1, keepdim=True)), 1).cpu().detach())
        weights_second = (weights_second[0].cpu().detach(),
                          torch.cat((weights_second[1].mean(dim=1, keepdim=True),
                                    weights_second[2].mean(dim=1, keepdim=True)), 1).cpu().detach())

        # 组合两层注意力权重
        att_weights = self.miu * weights_first[1] + (1 - self.miu) * weights_second[1]
        att_weights_all.append(att_weights)
        emb_out_avg = emb_out_avg + gene_emb

    # 计算多次运行的平均值
    if self.repeats > 1:
        att_weights_all = torch.stack((att_weights_all), 0)
    else:
        att_weights_all = att_weights_all[0].unsqueeze(0)
    emb_out_avg = emb_out_avg / self.repeats

    # 保存结果
    self.edge_index = data.edge_index.cpu()
    self._att_coefs = (weights_first[0], att_weights_all)
    self._node_embs = emb_out_avg
```

- **功能**：训练图神经网络模型并获取结果
- **技术细节**：
  - **重复训练**：运行多次(默认5次)训练以获得稳定结果
  - **优化目标**：最小化DeepGraphInfomax对比损失
  - **最佳模型选择**：保存训练过程中损失最小的模型
  - **注意力权重处理**：
    1. 对多头注意力结果取平均
    2. 合并入向和出向注意力权重
    3. 通过参数 `miu`(默认0.5)加权组合第一层和第二层注意力权重
  - **结果聚合**：计算多次运行的平均基因嵌入和注意力权重

## 6. 训练的关键函数

```python
def __train(data, model, optimizer):
    model.train()
    optimizer.zero_grad()
    pos_z, neg_z, summary = model(data)
    loss = model.loss(pos_z, neg_z, summary)
    loss.backward()
    optimizer.step()
    return float(loss.item())

def __get_encoder_results(data, model):
    model.eval()
    emb_last = model(data)
    return model.x_embs, model.att_weights_first, model.att_weights_second, emb_last
```

- **功能**：模型训练和推理的辅助函数
- **技术细节**：
  - **训练过程**：前向传播、计算损失、反向传播、更新参数
  - **获取结果**：从训练好的模型提取基因嵌入和注意力权重

## 7. 模型输出

图神经网络阶段的主要输出：

1. **基因嵌入向量**：`self._node_embs`，维度为[n_genes, output_dim]

   - 捕获基因的语义和功能相似性
   - 用于后续基因聚类和功能分析
2. **边注意力权重**：`self._att_coefs`，维度为[n_repeats, n_edges, 2]

   - 第一维：不同运行的结果
   - 第二维：每条边
   - 第三维：入向和出向注意力权重
   - 这些权重表示调控关系的强度，用于后续网络推断

## 图神经网络阶段的创新点

1. **双向图注意力**：同时考虑基因作为调控者和被调控者的角色
2. **多种注意力机制**：支持三种不同的注意力计算方式
3. **差异表达增强**：使用差异表达分数增强重要基因的影响
4. **自监督学习**：不依赖标记数据，使用DeepGraphInfomax学习表示
5. **多层表征学习**：通过多层图注意力捕获复杂的调控模式
6. **多次运行稳定性**：通过多次训练减少随机初始化带来的不稳定性

这一阶段是CEFCON的核心，它通过图注意力网络从基因表达数据和先验网络中学习细胞谱系特异的基因调控关系，输出的基因嵌入和注意力权重为后续的网络推断和关键调控因子识别提供了基础。

# 三、CEFCON网络推断阶段详细解读

## 整体架构

```
[图神经网络输出] ──→ [注意力权重处理] ──→ [边过滤与缩放] ──→ [阈值选择] ──→ [网络构建] ──→ [细胞谱系特异GRN]
```

网络推断阶段是CEFCON将学习到的注意力权重转化为可解释的基因调控网络(GRN)的关键步骤。

## 1. 输入准备

```python
def get_network(self,
                keep_self_loops: bool = True,
                edge_threshold_avgDegree: Optional[int] = 10,
                edge_threshold_zscore: Optional[float] = None,
                output_file: Optional[str] = None) -> nx.DiGraph:
  
    edge_index_ori = self.edge_index
    edge_index_with_selfloop, att_coefs_with_selfloop = self._att_coefs[0], self._att_coefs[1]

    # 原始注意力权重的处理
    ori_att_coefs_all = pd.DataFrame(
        {'from': edge_index_with_selfloop[0].numpy().astype(int),
         'to': edge_index_with_selfloop[1].numpy().astype(int),
         'att_coef_in': att_coefs_with_selfloop.mean(0, keepdim=False)[:, 0].numpy(),
         'att_coef_out': att_coefs_with_selfloop.mean(0, keepdim=False)[:, 1].numpy()}
    )
    ori_att_coefs_all['edge_idx_tmp'] = ori_att_coefs_all['from'].astype(str) + "|" + ori_att_coefs_all['to'].astype(str)
```

- **功能**：准备从图神经网络获得的边索引和注意力权重
- **技术细节**：
  - **输入参数**：
    - `keep_self_loops`：是否保留自环（默认True）
    - `edge_threshold_avgDegree`：基于平均度的边阈值（默认10）
    - `edge_threshold_zscore`：基于Z分数的边阈值（默认None）
    - `output_file`：输出文件路径（可选）
  - **注意力权重处理**：
    - 取多次运行的平均值
    - 分离入向和出向注意力权重
    - 创建带有临时边索引的DataFrame

## 2. 注意力权重缩放

```python
# 缩放注意力系数进行全局排序
scaled_att_coefs = []
g = nx.from_edgelist(edge_index_with_selfloop.numpy().T, create_using=nx.DiGraph)
for i in range(2):
    # i==0: 入向; i==1: 出向
    att_coef_i = att_coefs_with_selfloop[:, :, i]  # shape: [num_repeats, num_edges]

    # 通过乘以中心节点的度进行缩放
    # 这确保了注意力权重反映全局重要性
    if i == 0:
        d = pd.DataFrame(g.in_degree(), columns=['index', 'degree'])
    else:  # i == 1
        d = pd.DataFrame(g.out_degree(), columns=['index', 'degree'])
    d.index = d['index']
    # att_coef_i * degree_i
    att_coef_i = att_coef_i * np.array(d.loc[edge_index_with_selfloop[1 - i, :].numpy(), 'degree'])
    att_coef_i = att_coef_i.t()  # shape: [num_edges, num_repeats]

    # 处理自环
    if not keep_self_loops:
        # 移除所有自环
        edge_index, att_coef_i = remove_self_loops(edge_index_with_selfloop, att_coef_i)
    else:
        # 只保留先验网络中的自环
        prior_selfloop_nodes = edge_index_ori[0, edge_index_ori[0] == edge_index_ori[1]]
        selfloop_not_in_prior = (edge_index_with_selfloop[0] == edge_index_with_selfloop[1]) & \
                               ~(edge_index_with_selfloop[0][..., None] == prior_selfloop_nodes).any(-1)
        edge_index, att_coef_i = (edge_index_with_selfloop[:, ~selfloop_not_in_prior],
                                 att_coef_i[~selfloop_not_in_prior])

    # 存储缩放后的注意力系数
    scaled_att_coefs = scaled_att_coefs + [att_coef_i.clone()]

# 组合入向和出向缩放注意力权重
scaled_att_coefs_combined = (scaled_att_coefs[0] * 0.5) + (scaled_att_coefs[1] * 0.5)
```

- **功能**：缩放注意力权重以反映全局重要性
- **技术细节**：
  - **缩放策略**：
    - 入向权重乘以节点的入度
    - 出向权重乘以节点的出度
    - 这种缩放确保了高度连接的节点(hub)的边权重更高
  - **处理自环**：
    - 如果 `keep_self_loops=False`，移除所有自环
    - 如果 `keep_self_loops=True`，只保留先验网络中的自环
  - **权重组合**：入向和出向缩放权重各占50%

## 3. 数据框组织

```python
# 创建包含所有边信息的DataFrame
scaled_att_coefs_all = pd.DataFrame(
    {'from': edge_index[0].numpy().astype(int),
     'to': edge_index[1].numpy().astype(int),
     'weights_in': scaled_att_coefs[0].mean(1, keepdim=False).numpy(),
     'weights_out': scaled_att_coefs[1].mean(1, keepdim=False).numpy(),
     'weights_combined': scaled_att_coefs_combined.mean(1, keepdim=False).numpy(),
     'weights_std': scaled_att_coefs_combined.std(1, keepdim=False).numpy()}
)
```

- **功能**：将缩放后的注意力权重组织为DataFrame
- **技术细节**：
  - **列内容**：
    - `from`、`to`：边的起点和终点
    - `weights_in`：入向缩放注意力权重
    - `weights_out`：出向缩放注意力权重
    - `weights_combined`：组合的缩放注意力权重
    - `weights_std`：多次运行的标准差，反映权重稳定性

## 4. 边筛选与阈值应用

```python
# 如果重复次数≥10，则过滤变异系数(CV)高的边
if scaled_att_coefs[0].shape[1] >= 10:
    CV = scaled_att_coefs_all['weights_std'] / (scaled_att_coefs_all['weights_combined'] + 1e-9)
    cv_filter = (CV < 0.2)
else:
    cv_filter = np.ones(scaled_att_coefs_combined.shape[0], dtype=bool)

# 根据设定阈值选择边
att_weights_combined = scaled_att_coefs_all['weights_combined']
if edge_threshold_avgDegree is not None:
    # 选择前(N_nodes*edge_threshold_avgDegree)条边
    filtered_edge_idx = np.argsort(-att_weights_combined)[0:g.number_of_nodes() * edge_threshold_avgDegree]
    filtered_edge_idx = np.intersect1d(filtered_edge_idx, np.where(cv_filter)[0])
else:
    if edge_threshold_zscore is not None:
        # 选择Z分数>阈值的边
        m, s = att_weights_combined.mean(), att_weights_combined.std()
        edge_threshold_weight = m + (edge_threshold_zscore * s)
        filtered_edge_idx = np.where((att_weights_combined > edge_threshold_weight) & cv_filter)[0]
    else:  # 所有边
        filtered_edge_idx = list(range(len(att_weights_combined)))

# 筛选结果
scaled_att_coefs_filtered = scaled_att_coefs_all.iloc[filtered_edge_idx, :].copy()
```

- **功能**：筛选稳定的高权重边
- **技术细节**：
  - **变异系数筛选**：
    - 当重复次数≥10时，计算变异系数(CV = std/mean)
    - 过滤CV > 0.2的边，这些边在多次运行中不稳定
  - **两种阈值策略**：
    - **基于平均度**：选择权重最高的前(节点数 × avgDegree)条边
    - **基于Z分数**：选择权重超过(均值 + z × 标准差)的边
  - **目的**：控制网络复杂度，只保留最强且稳定的调控关系

## 5. 原始注意力与缩放注意力整合

```python
# 获取原始注意力系数
ori_att_coefs_filtered = ori_att_coefs_all.loc[
    ori_att_coefs_all['edge_idx_tmp'].isin(
        scaled_att_coefs_filtered['from'].astype(str) + "|" + scaled_att_coefs_filtered['to'].astype(str)),
    ['from', 'to', 'att_coef_in', 'att_coef_out']
].copy()

# 合并原始和缩放注意力系数
net_filtered_df = pd.merge(scaled_att_coefs_filtered, ori_att_coefs_filtered, on=['from', 'to'], how='inner')
```

- **功能**：整合原始注意力和缩放注意力信息
- **技术细节**：
  - 筛选与已选边对应的原始注意力系数
  - 通过 `from`和 `to`列合并两个DataFrame
  - 最终得到包含所有权重信息的边列表

## 6. 网络构建与检查

```python
# 创建NetworkX图对象
G_nx = nx.from_pandas_edgelist(net_filtered_df, source='from', target='to', edge_attr=True,
                              create_using=nx.DiGraph)

# 检查最大连通分量大小
num_nodes = len(set(edge_index_with_selfloop[0].numpy()).union(set(edge_index_with_selfloop[1].numpy())))
largest_components = max(nx.weakly_connected_components(G_nx), key=len)
if (len(largest_components) / num_nodes) < 0.5:
    print(f"Warning: The largest connected component only contains "
          f"{100*len(largest_components)/num_nodes:.1f}% of all nodes.")

# 将节点索引替换为基因名称
idx_to_name = self._idx_GeneName_map['geneName'].to_dict()
G_predicted = nx.relabel_nodes(G_nx, idx_to_name)

# 保存结果
if isinstance(output_file, str):
    edge_list = pd.DataFrame([(e[0], e[1], e[2]['weights_combined']) 
                             for e in G_predicted.edges(data=True)],
                            columns=['from', 'to', 'weight'])
    edge_list.to_csv(output_file, index=False)

return G_predicted
```

- **功能**：构建最终的基因调控网络
- **技术细节**：
  - **网络表示**：使用NetworkX的DiGraph表示有向调控网络
  - **连通性检查**：确保最大连通分量包含足够比例的节点
  - **节点重标记**：将节点索引转换回基因名称
  - **结果输出**：可选地将边列表保存为CSV文件

## 7. 基因嵌入向量导出

```python
def get_gene_embedding(self, output_file: Optional[str] = None) -> pd.DataFrame:
    """
    获取基因嵌入向量
    """
    gene_embs = self._node_embs
    idx_to_name = self._idx_GeneName_map['geneName'].to_dict()
    gene_embs_df = pd.DataFrame(gene_embs, index=[idx_to_name[i] for i in range(len(gene_embs))])
  
    if isinstance(output_file, str):
        gene_embs_df.to_csv(output_file)
  
    return gene_embs_df
```

- **功能**：导出基因嵌入向量
- **技术细节**：
  - 将图神经网络学习的节点嵌入向量转换为DataFrame
  - 使用基因名称作为索引
  - 可选地保存到CSV文件

## 8. 网络推断阶段的关键创新

### 8.1 注意力缩放机制

CEFCON的一个关键创新是缩放注意力权重，考虑节点的全局拓扑特性：

```python
# att_coef_i * degree_i
att_coef_i = att_coef_i * np.array(d.loc[edge_index_with_selfloop[1 - i, :].numpy(), 'degree'])
```

这种缩放策略：

- 增强了高度连接节点(hub)的边权重
- 平衡了局部注意力分数和全局拓扑重要性
- 确保了最终网络反映关键调控关系

### 8.2 多层注意力集成

通过参数 `miu`控制对不同层注意力权重的依赖：

```python
att_weights = self.miu * weights_first[1] + (1 - self.miu) * weights_second[1]
```

- 默认 `miu=0.5`，即同等考虑第一层和第二层注意力
- 第一层捕获直接相互作用
- 第二层捕获更复杂的间接调控关系

### 8.3 双向注意力整合

入向和出向注意力权重平等贡献：

```python
scaled_att_coefs_combined = (scaled_att_coefs[0] * 0.5) + (scaled_att_coefs[1] * 0.5)
```

这确保同时考虑基因作为调控者和被调控者的角色。

### 8.4 稳定性过滤

使用变异系数(CV)过滤不稳定边：

```python
CV = scaled_att_coefs_all['weights_std'] / (scaled_att_coefs_all['weights_combined'] + 1e-9)
cv_filter = (CV < 0.2)
```

这提高了推断网络的可靠性，只保留在多次运行中始终强调的一致边。

## 9. 网络推断阶段输出

最终输出是一个NetworkX的DiGraph对象：

- 节点是基因
- 有向边表示潜在的调控关系
- 边属性包含多种权重指标：
  - `weights_in`：入向缩放权重
  - `weights_out`：出向缩放权重
  - `weights_combined`：组合权重
  - `att_coef_in`：原始入向注意力系数
  - `att_coef_out`：原始出向注意力系数

这一细胞谱系特异性的GRN是CEFCON的核心输出，为后续驱动调控因子识别和RGM活性分析提供基础。

## 网络推断阶段的生物学意义

1. **谱系特异性**：学习到的网络反映细胞谱系特异的调控关系
2. **调控强度量化**：边权重表示调控关系的强度
3. **平衡先验知识与数据驱动**：整合先验网络结构和基因表达数据学习的关系
4. **降低复杂度**：通过阈值选择，保留最显著的调控关系，使网络更易解释

通过这一阶段，CEFCON将深度学习模型的输出转化为具有生物意义的调控网络，为理解基因调控机制提供新视角。

# 四、CEFCON驱动调控因子识别阶段详细解读

## 整体架构

```
[细胞谱系GRN] ──→ [基因影响分数计算] ──┬──→ [MDS控制方法] ─────┐
                         │              │                      │
                         │              └──→ [MFVS控制方法] ────┼──→ [驱动因子合并] ──→ [驱动调控因子]
                         │                                     │
                         └────────────────────────────────────┘
```

驱动调控因子识别是CEFCON的核心创新点之一，它使用网络控制理论从推断的GRN中识别关键调控基因。

## 1. 基因影响分数计算

在 `CefconResults`类中的 `gene_influence_score`方法:

```python
def gene_influence_score(self):
    """
    计算基因影响分数
    """
    influence_score = pd.DataFrame(np.zeros((len(self.network.nodes), 2)),
                                  index=sorted(self.network.nodes),
                                  columns=['out', 'in'])
  
    for i, v in enumerate(['in', 'out']):
        # 出度类型的影响从入向网络获取;
        # 入度类型的影响从出向网络获取.
        gene_att_score = np.sum(nx.to_numpy_array(self.network,
                                                 nodelist=sorted(self.network.nodes),
                                                 dtype='float32',
                                                 weight='weights_{}'.format(v)),  # {}_weights
                                axis=1 - i)
        influence_score.iloc[:, i] = np.log1p(gene_att_score).flatten().tolist()

    # 加权组合入度和出度影响分数
    lam = 0.8
    influence_score['influence_score'] = lam * influence_score.loc[:, 'out'] + \
                                        (1 - lam) * influence_score.loc[:, 'in']
    influence_score.rename(columns={'out': 'score_out', 'in': 'score_in'}, inplace=True)
    influence_score = influence_score.sort_values(by='influence_score', ascending=False)
    self.influence_score = influence_score
```

- **功能**: 为网络中的每个基因计算影响分数
- **技术细节**:
  - **双向影响考量**:
    - **出度影响(score_out)**: 基因作为调控者的影响力，从入向网络的边权重累加
    - **入度影响(score_in)**: 基因作为被调控目标的影响力，从出向网络的边权重累加
  - **对数变换**: 使用 `log1p`函数(log(1+x))对原始累加分数进行对数变换，减小极值影响
  - **加权组合**:
    - 使用参数 `lam=0.8`偏重出度影响(80%)和入度影响(20%)
    - 公式: `influence_score = 0.8 * score_out + 0.2 * score_in`
  - **排序**: 按总影响分数降序排列

## 2. 网络控制理论基础

CEFCON使用两种经典的网络控制理论方法识别驱动节点:

### 2.1 最小支配集(MDS, Minimum Dominating Set)

- **定义**: 网络中的一个节点子集S，使得网络中的每个节点要么属于S，要么与S中的至少一个节点相邻
- **生物学意义**: MDS中的节点能够直接"支配"(影响)网络中的所有其他节点
- **应用**: 识别能够直接调控大量目标基因的关键调控因子

### 2.2 最小反馈顶点集(MFVS, Minimum Feedback Vertex Set)

- **定义**: 网络中的一个节点子集S，移除S后网络变成无环图(DAG)
- **生物学意义**: MFVS中的节点控制网络中的所有反馈环路
- **应用**: 识别参与调控环路的关键基因，这些基因通常在动态调控中起关键作用

## 3. 驱动调控因子识别流程

在 `CefconResults`类中的 `driver_regulators`方法:

```python
def driver_regulators(self,
                     topK: int = 100,
                     solver: str = 'GUROBI',
                     return_value: bool = False,
                     output_file: Optional[str] = None):
    """
    从构建的细胞谱系特异性GRN中识别驱动调控因子。
    """
    if self.influence_score is None:
        self.gene_influence_score()

    self.driver_regulator, self._out_critical_genes, self._in_critical_genes = driver_regulators(
        self.network,
        self.influence_score,
        topK=topK,
        driver_union=True,
        solver=solver)
  
    self.driver_regulator['is_TF'] = self.driver_regulator.index.isin(self.TFs)

    if isinstance(output_file, str):
        self.driver_regulator.to_csv(output_file)
    if return_value:
        return self.driver_regulator
```

- **功能**: 调用 `driver_regulators`函数识别驱动调控因子
- **参数**:
  - `topK`: 考虑的候选驱动基因数量(默认100)
  - `solver`: 求解ILP问题的优化器(默认'GUROBI')
  - `output_file`: 输出文件路径(可选)
- **返回**:
  - `driver_regulator`: 驱动调控因子DataFrame
  - `_out_critical_genes`: 出度关键基因集
  - `_in_critical_genes`: 入度关键基因集
- **额外标记**: 添加 `is_TF`列标识是否为转录因子

## 4. 核心算法实现

在 `driver_regulators.py`中实现了 `driver_regulators`函数:

```python
def driver_regulators(directed_graph: nx.DiGraph,
                      nodes_importance: pd.DataFrame,
                      topK: int = 100,
                      driver_union: bool = True,
                      solver: str = 'GUROBI') -> pd.DataFrame:
    """
    识别网络中的驱动调控因子。
  
    参数:
        directed_graph: 有向图，表示基因调控网络
        nodes_importance: 节点重要性分数，包含'influence_score'列
        topK: 按影响力分数筛选的顶部基因数量
        driver_union: 是否使用MDS和MFVS结果的并集作为驱动调控因子
        solver: ILP求解器，可选'GUROBI'、'GLPK_MI'或'SCIP'
  
    返回:
        带有驱动调控因子信息的DataFrame
    """
  
    print('[2] - Identifying driver regulators...')
  
    # 获取顶部K个候选驱动基因
    if topK >= len(nodes_importance):
        topK = len(nodes_importance)
    if topK <= 0:
        print('The whole network is used to determine driver regulators.')
        subG = directed_graph
    else:
        print('Top {} genes with the highest influence scores are selected as candidates.'.format(topK))
        candidate_drivers = set(nodes_importance.index[:topK])
        subG = directed_graph.subgraph(candidate_drivers)
  
    # [1] 使用MDS识别出度关键节点(驱动基因)
    print('\nMethod 1 - Identifying out-degree critical genes by MDS control:')
    MDS_driver_set, MDS_inter_set = MDScontrol(subG, solver=solver)
    out_critical_genes = MDS_driver_set
  
    # [2] 使用MFVS识别入度关键节点(驱动基因)
    print('\nMethod 2 - Identifying in-degree critical genes by MFVS control:')
    MFVS_driver_set = MFVScontrol(subG, nodes_importance, solver=solver)
    in_critical_genes = MFVS_driver_set
  
    # 创建驱动基因DataFrame
    driver_info = pd.DataFrame(index=nodes_importance.index)
    driver_info['is_MDS_driver'] = driver_info.index.isin(out_critical_genes)
    driver_info['is_MFVS_driver'] = driver_info.index.isin(in_critical_genes)
  
    # 根据driver_union参数组合MDS和MFVS结果
    if driver_union:  # 使用MDS和MFVS的并集
        driver_info['is_driver_regulator'] = driver_info['is_MDS_driver'] | driver_info['is_MFVS_driver']
    else:  # 使用MDS和MFVS的交集
        driver_info['is_driver_regulator'] = driver_info['is_MDS_driver'] & driver_info['is_MFVS_driver']
  
    # 添加影响分数
    driver_info = pd.merge(driver_info, nodes_importance, left_index=True, right_index=True, how='left')
  
    # 处理结果并输出信息
    n_driver = driver_info['is_driver_regulator'].sum()
    n_out = driver_info['is_MDS_driver'].sum()
    n_in = driver_info['is_MFVS_driver'].sum()
    n_both = (driver_info['is_MDS_driver'] & driver_info['is_MFVS_driver']).sum()
    print(f"\n{n_driver} driver regulators are identified "
          f"({n_out} from MDS, {n_in} from MFVS, {n_both} from both methods).")
  
    return driver_info, out_critical_genes, in_critical_genes
```

- **功能**: 整合多种网络控制方法识别驱动调控因子
- **技术细节**:
  - **候选基因筛选**:
    - 使用影响分数的前topK个基因作为候选
    - 为了提高计算效率，仅在候选子图上执行控制算法
  - **MDS计算**:
    - 调用 `MDScontrol`函数识别最小支配集
    - 返回出度关键基因(能够"支配"其他节点的基因)
  - **MFVS计算**:
    - 调用 `MFVScontrol`函数识别最小反馈顶点集
    - 返回入度关键基因(控制环路的基因)
  - **结果组合**:
    - `driver_union=True`: MDS和MFVS结果的并集(默认)
    - `driver_union=False`: MDS和MFVS结果的交集

## 5. MDS算法详解

MDS算法在 `driver_regulators.py`中实现:

```python
def _MDS_graph_reduction(directed_graph: nx.DiGraph):
    """
    图约简步骤：通过拓扑特征识别关键节点和冗余节点
    """
    # 关键节点是驱动节点
    critical_nodes = set()
    redundant_nodes = set()

    # 关键节点条件1: 源节点(入度为0的节点)是关键节点
    critical_nodes.update(_root_nodes(directed_graph))
    remain_nodes = set(directed_graph.nodes()) - critical_nodes

    # 迭代图约简
    noChange = False
    while not noChange:
        noChange = True

        # 关键节点条件2: 有至少两条指向出度为0且入度为1的节点的边的节点
        in1out0_nodes = set([n for n in directed_graph.nodes() if (directed_graph.in_degree(n) == 1
                                                                  and directed_graph.out_degree(n) == 0)])
        add_critical = set([n for n in list(remain_nodes - in1out0_nodes)
                           if (len(set(directed_graph.successors(n)).intersection(in1out0_nodes)) > 1)])
        # 处理新发现的关键节点...

        # 冗余节点条件: 出度为0且有来自关键节点的入边的节点
        add_redundant = set([n for n in list(remain_nodes)
                            if (len(set(directed_graph.predecessors(n)).intersection(critical_nodes)) > 0
                                and (directed_graph.out_degree(n) == 0))])
        # 处理新发现的冗余节点...

    return directed_graph, critical_nodes, redundant_nodes

def MDScontrol(directed_graph: nx.DiGraph, solver='GUROBI'):
    """
    使用MDS方法识别驱动节点
    """
    print('  Solving MDS problem...')
    directed_graph.remove_edges_from(nx.selfloop_edges(directed_graph))
    reduced_graph = nx.DiGraph(directed_graph)
    intermittent_nodes = set()
    MDS_driver_set = set()

    # 图约简
    reduced_graph, critical_nodes, redundant_nodes = _MDS_graph_reduction(reduced_graph)
    print('    {} critical nodes are found.'.format(len(critical_nodes)))

    # 如果图约简后还有节点，使用ILP求解最小支配集问题
    reduced_graph.remove_nodes_from(list(nx.isolates(reduced_graph)))
    if reduced_graph.number_of_nodes() > 0:
        # 构建邻接矩阵(包括自环)
        A = nx.to_numpy_array(reduced_graph)
        A = A + np.eye(A.shape[0])
  
        # 定义优化变量
        x = cvx.Variable(reduced_graph.number_of_nodes(), boolean=True)
  
        # 定义约束条件: 每个节点要么在支配集中，要么至少有一个邻居在支配集中
        constraints = [A @ x >= np.ones(reduced_graph.number_of_nodes())]
  
        # 定义优化目标: 最小化支配集大小
        obj = cvx.Minimize(cvx.sum(x))
  
        # 求解问题
        prob = cvx.Problem(obj, constraints)
        prob.solve(solver=cvx.GUROBI, verbose=False)
  
        # 获取结果
        nodes_idx_map = dict(zip(range(reduced_graph.number_of_nodes()), reduced_graph.nodes()))
        mds_nodes = set([v for k, v in nodes_idx_map.items() if x.value[k] == 1])
        MDS_driver_set = critical_nodes.union(mds_nodes)
        intermittent_nodes = set(reduced_graph.nodes()) - MDS_driver_set

    return MDS_driver_set, intermittent_nodes
```

- **功能**: 使用图约简和整数线性规划(ILP)求解最小支配集问题
- **技术细节**:
  - **图约简阶段**:
    - 通过拓扑特征快速识别必然的驱动节点(关键节点)和必然非驱动节点(冗余节点)
    - 关键节点包括: 源节点(入度为0)和有多条出边指向特定类型节点的节点
    - 冗余节点包括: 出度为0且有来自关键节点的入边的节点
  - **ILP求解阶段**:
    - 在约简后的图上构建整数线性规划问题
    - 优化目标: 最小化选择的节点数量
    - 约束条件: 每个节点要么被选择，要么至少有一个邻居被选择
    - 使用GUROBI/GLPK_MI/SCIP求解器求解

## 6. MFVS算法详解

MFVS算法在 `driver_regulators.py`中实现:

```python
def _CORE(directed_graph: nx.DiGraph, nodes_importance: pd.DataFrame, S: set):
    """
    CORE约简: 将高于阈值的重要节点直接加入FVS
    """
    # 选择影响分数最高的节点作为FVS
    remove_set = set()
    temp = nodes_importance.iloc[:int(nodes_importance.shape[0] * 0.5), :].copy()
    remove_set = set(temp.index.tolist()) & set(directed_graph.nodes())
    S = S.union(remove_set)
    directed_graph.remove_nodes_from(list(remove_set))
    return directed_graph, S, (len(remove_set) > 0)

def _SCC(directed_graph: nx.DiGraph):
    """
    SCC约简: 将图分解为强连通分量
    """
    SCCs = [c for c in nx.strongly_connected_components(directed_graph) if len(c) > 1]
    # 处理强连通分量...

def MFVScontrol(directed_graph: nx.DiGraph, nodes_importance: pd.DataFrame, solver='GUROBI'):
    """
    使用MFVS方法识别驱动节点
    """
    print('  Solving MFVS problem...')
    directed_graph.remove_edges_from(nx.selfloop_edges(directed_graph))
    reduced_graph = nx.DiGraph(directed_graph)
    MFVS_driver_set = set()

    # 图约简
    noChange = False
    while not noChange:
        noChange = True
  
        # 各种约简规则:
        # 1. 移除孤立节点或叶节点
        reduced_graph, non_change1 = _in0out0(reduced_graph)
        noChange = noChange and non_change1
  
        # 2. 处理自环
        reduced_graph, MFVS_driver_set, non_change2 = _selfloop(reduced_graph, MFVS_driver_set)
        noChange = noChange and non_change2
  
        # 3. 入度为1的节点处理
        reduced_graph, non_change3 = _in1(reduced_graph)
        noChange = noChange and non_change3
  
        # 4. 出度为1的节点处理
        reduced_graph, non_change4 = _out1(reduced_graph)
        noChange = noChange and non_change4
  
        # 5. 强连通分量处理
        reduced_graph, non_change5 = _PIE(reduced_graph)
        noChange = noChange and non_change5
  
        # 6. 核心节点处理(高影响分数节点)
        reduced_graph, MFVS_driver_set, non_change6 = _CORE(reduced_graph, nodes_importance, MFVS_driver_set)
        noChange = noChange and non_change6
  
        # 7. 强连通分量分解
        reduced_graph, MFVS_driver_set, non_change7 = _SCC(reduced_graph, nodes_importance, MFVS_driver_set, solver)
        noChange = noChange and non_change7

    # 如果约简后还有环路，使用ILP求解
    if len(list(nx.simple_cycles(reduced_graph))) > 0:
        # 使用ILP求解MFVS问题
        # ...类似MDS的ILP求解过程
  
    print('  {} MFVS driver genes are found.'.format(len(MFVS_driver_set)))
    return MFVS_driver_set
```

- **功能**: 使用多种图约简规则和整数线性规划求解最小反馈顶点集问题
- **技术细节**:
  - **图约简阶段**:
    - 应用多种约简规则减小问题规模:
      - `_in0out0`: 移除入度或出度为0的节点
      - `_selfloop`: 处理自环(有指向自身的边的节点)
      - `_in1`: 处理入度为1的节点
      - `_out1`: 处理出度为1的节点
      - `_PIE`: 保留强连通分量内的边
      - `_CORE`: 直接选择高影响分数节点
      - `_SCC`: 分解强连通分量
  - **ILP求解阶段**:
    - 在约简后的图上构建整数线性规划问题
    - 优化目标: 最小化选择的节点数量
    - 约束条件: 选择的节点必须覆盖所有环路
    - 使用GUROBI/GLPK_MI/SCIP求解器求解

## 7. 驱动调控因子的分析与利用

识别的驱动调控因子具有多种用途:

### 7.1 驱动调控因子属性分析

```python
# 在driver_regulators函数返回的DataFrame中
driver_info['is_TF'] = driver_info.index.isin(self.TFs)  # 标记转录因子
```

- 标记每个驱动调控因子是否为转录因子
- 分析不同类型驱动因子的比例和特点

### 7.2 用于RGM定义

在 `RGM_activity`方法中:

```python
drivers = set(self.driver_regulator[self.driver_regulator['is_driver_regulator']].index)
out_hub_nodes = self._out_critical_genes.intersection(drivers)
out_RGMs = {i + '_out({})': list(set(network.successors(i)).intersection(DEgenes)) for i in out_hub_nodes}
in_hub_nodes = self._in_critical_genes.intersection(drivers)
in_RGMs = {i + '_in({})': list(set(network.predecessors(i)).intersection(DEgenes)) for i in in_hub_nodes}
```

- 使用MDS识别的出度关键基因定义前馈RGM(驱动调控因子及其目标基因)
- 使用MFVS识别的入度关键基因定义反馈RGM(驱动调控因子及其调控者)

## 8. 驱动调控因子识别的创新点

### 8.1 多方法集成

CEFCON创新地结合了两种互补的网络控制方法:

- **MDS**: 识别直接调控多个目标的关键基因
- **MFVS**: 识别参与调控反馈环路的关键基因
- **并集/交集策略**: 灵活选择两种方法的组合方式

### 8.2 高效图约简

在应用ILP求解前使用多种图约简策略:

- 降低计算复杂度
- 处理大规模网络
- 通过拓扑特征快速识别关键节点

### 8.3 影响分数引导

- 使用GNN学习的注意力权重计算影响分数
- 在CORE约简中优先选择高影响分数节点
- 候选基因预筛选提高计算效率和生物学相关性

### 8.4 考虑双向调控关系

- 分别考虑基因作为调控者(出度)和被调控者(入度)的角色
- 出度影响和入度影响加权组合(权重8:2)

## 9. 驱动调控因子的生物学意义

### 9.1 主调控基因

- 驱动调控因子是细胞谱系发育过程中的关键调控者
- 控制下游基因表达和细胞命运决定

### 9.2 稳态维持者

- MFVS识别的因子控制反馈环路
- 维持细胞稳态或触发状态转变

### 9.3 干预靶点

- 驱动调控因子是潜在的干预靶点
- 通过操纵少量关键基因可能影响整个网络的动态

### 9.4 疾病相关因子

- 驱动调控因子的突变或异常表达可能导致疾病
- 揭示疾病发生和进展的机制

CEFCON的驱动调控因子识别方法结合了网络拓扑特性和基因影响分数，以计算效率高且生物学意义强的方式识别关键调控基因，为理解细胞命运决定和疾病机制提供了新视角。

# 五、CEFCON的RGM活性分析详细解读

## 1. RGM的概念与定义

```
[驱动调控因子] ──→ [定义RGM] ──→ [计算活性分数] ──→ [细胞分群] ──→ [功能解释]
```

RGM (Regulon-like Gene Module，类调控子基因模块) 是CEFCON的重要创新概念：

- **定义**：以驱动调控因子为中心，包含其直接调控的目标基因集合
- **类型**：
  - **出向RGM (Out-RGM)**：由MDS识别的驱动调控因子及其调控的下游目标基因组成
  - **入向RGM (In-RGM)**：由MFVS识别的驱动调控因子及其上游调控基因组成
- **生物学意义**：类似于传统的调控子(regulon)概念，但基于网络学习和控制理论构建

## 2. RGM的构建流程

`RGM_activity`方法中的RGM构建：

```python
def RGM_activity(self, num_workers: int = 8, return_value: bool = False, output_file: Optional[str] = None):
    """
    选择驱动调控因子的RGM并计算它们在每个细胞中的活性。
    活性分数基于AUCell计算，来自`pyscenic`包。
    """
    print('[3] - Identifying regulon-like gene modules...')

    if self.driver_regulator is None:
        raise ValueError(
            f'No result found for driver regulators. Please run `cefcon.NetModel.driver_regulators` first.'
        )
  
    from pyscenic.aucell import aucell
    from ctxcore.genesig import GeneSignature

    network = self.network
    DEgenes = self.DEgenes
    drivers = set(self.driver_regulator[self.driver_regulator['is_driver_regulator']].index)

    # 构建出向RGM
    out_hub_nodes = self._out_critical_genes.intersection(drivers)
    out_RGMs = {i + '_out({})'.format(len(list(set(network.successors(i)).intersection(DEgenes)))):
                    list(set(network.successors(i)).intersection(DEgenes)) for i in out_hub_nodes
                if len(set(network.successors(i)).intersection(DEgenes)) >= 10}
  
    # 构建入向RGM
    in_hub_nodes = self._in_critical_genes.intersection(drivers)
    in_RGMs = {i + '_in({})'.format(len(list(set(network.predecessors(i)).intersection(DEgenes)))):
                   list(set(network.predecessors(i)).intersection(DEgenes)) for i in in_hub_nodes
               if len(set(network.predecessors(i)).intersection(DEgenes)) >= 10}
```

- **技术细节**：
  - **输入**：驱动调控因子、构建的网络、差异表达基因集
  - **过滤步骤**：
    1. 获取被识别为驱动调控因子的基因
    2. 分别从MDS和MFVS结果中筛选驱动调控因子
    3. 提取每个驱动因子的目标基因/调控基因
    4. 与差异表达基因集取交集，增强生物学相关性
    5. 筛选至少包含10个基因的模块，确保统计意义
  - **命名规则**：`基因名_方向(基因数量)`，例如 `SOX2_out(24)`

## 3. RGM活性评分方法

```python
# 使用AUCell计算活性分数
out_RGMs = [GeneSignature(name=k, gene2weight=v) for k, v in out_RGMs.items()]
in_RGMs = [GeneSignature(name=k, gene2weight=v) for k, v in in_RGMs.items()]

if len(out_RGMs) > 0:
    auc_mtx_out = aucell(self.expression_data, out_RGMs, num_workers=num_workers, auc_threshold=0.25,
                          normalize=False)
    # 为每个RGM生成Z-score以便比较
    auc_mtx_out_Z = pd.DataFrame(index=auc_mtx_out.index, columns=list(auc_mtx_out.columns), dtype=float)
    for col in list(auc_mtx_out.columns):
        auc_mtx_out_Z[col] = (auc_mtx_out[col] - auc_mtx_out[col].mean()) / auc_mtx_out[col].std(ddof=0)

if len(in_RGMs) > 0:
    auc_mtx_in = aucell(self.expression_data, in_RGMs, num_workers=num_workers, auc_threshold=0.25,
                        normalize=False)
    # Z-score标准化
    # ...类似处理
```

- **评分方法**：

  - **AUCell算法**：来自pySCENIC包，计算每个细胞中RGM的活性
  - **基本原理**：
    1. 对每个细胞中的所有基因按表达量排序
    2. 对每个RGM，计算其基因在排序列表中的累积分布
    3. 计算曲线下面积(AUC)作为活性分数
  - **参数设置**：
    - `auc_threshold=0.25`：AUC计算的阈值
    - `normalize=False`：保留原始AUC值
- **标准化**：

  - 对原始AUC分数进行Z-score标准化
  - 使不同RGM的活性分数可比较
  - 公式：`Z = (X - μ) / σ`，其中μ是均值，σ是标准差

## 4. 合并与保存结果

```python
if (len(out_RGMs) > 0) & (len(in_RGMs) > 0):
    auc_mtx = pd.merge(auc_mtx_out, auc_mtx_in, how='inner', left_index=True, right_index=True)
    # Z-score标准化
    # ...

RGMs = out_RGMs + in_RGMs
RGMs_AUCell_dict = {'RGMs': RGMs, 'aucell': auc_mtx,
                   'aucell_out': auc_mtx_out, 'aucell_in': auc_mtx_in}
self.RGMs_AUCell_dict = RGMs_AUCell_dict

if isinstance(output_file, str):
    pd_RGMs = pd.DataFrame(index=['genes'], columns=auc_mtx.columns)
    for r in RGMs:
        pd_RGMs.at['genes', r.name] = list(r.gene2weight.keys())
    result = pd.concat([pd_RGMs, auc_mtx], axis=0)
    result.columns.name = 'RGM'
    result.index.name = 'Cell'
    result.to_csv(output_file)
```

- **结果组织**：
  - **RGMs**: 所有RGM对象的列表
  - **aucell**: 合并的AUC活性矩阵（细胞×RGM）
  - **aucell_out/aucell_in**: 分别保存出向和入向RGM的活性
- **输出格式**：
  - CSV格式，包含RGM成员基因和活性分数
  - 行：细胞，列：RGM
  - 第一行：每个RGM的成员基因列表

## 5. RGM可视化与分析

CEFCON提供了多种分析RGM的方法：

### 5.1 热图可视化

```python
def plot_RGM_activity_heatmap(self, show_top_RGMs: int = 20, cluster_cells: bool = True,
                             cluster_RGMs: bool = True, save_path: Optional[str] = None):
    """
    绘制RGM活性热图
    """
    if self.RGMs_AUCell_dict is None:
        raise ValueError("Please run `RGM_activity` first!")
  
    # 提取活性矩阵
    auc_mtx = self.RGMs_AUCell_dict['aucell']
  
    # 计算RGM方差并选择前n个
    auc_mtx_var = auc_mtx.var(axis=0).sort_values(ascending=False)
    if show_top_RGMs >= len(auc_mtx_var):
        show_top_RGMs = len(auc_mtx_var)
    auc_mtx_plot = auc_mtx[auc_mtx_var.index[:show_top_RGMs]]
  
    # 绘制热图
    plt.figure(figsize=(max(6, int(show_top_RGMs / 3)), 8))
    sns.clustermap(auc_mtx_plot, cmap='viridis', z_score=0, 
                   row_cluster=cluster_cells, col_cluster=cluster_RGMs,
                   yticklabels=False)
    # ...其他绘图细节
```

- **功能**：可视化细胞中RGM活性模式
- **关键特性**：
  - 默认选择变异度最高的前20个RGM
  - 支持对细胞和RGM进行聚类
  - Z-score标准化以突出活性差异

### 5.2 细胞聚类分析

```python
def cluster_cell_by_RGM(auc_mtx, true_cell_label, method='ward', k=None):
    """
    基于RGM活性矩阵使用层次聚类对细胞分群
    """
    # Z-score标准化
    auc_mtx_Z = pd.DataFrame(index=auc_mtx.index, columns=list(auc_mtx.columns))
    for row in list(auc_mtx.index):
        auc_mtx_Z.loc[row, :] = (auc_mtx.loc[row, :] - auc_mtx.loc[row, :].mean()) / auc_mtx.loc[row, :].std(ddof=0)
  
    # 聚类
    ac = AgglomerativeClustering(n_clusters=k, affinity='euclidean', linkage=method).fit(auc_mtx_Z)
    predicted_cell_label = ac.labels_
  
    # 计算与真实标签的一致性
    NMIs = normalized_mutual_info_score(true_cell_label, predicted_cell_label)
    ARIs = adjusted_rand_score(true_cell_label, predicted_cell_label)
```

- **功能**：基于RGM活性对细胞进行聚类
- **技术细节**：
  - 使用层次聚类算法
  - 支持多种链接方法：ward、complete、average、single
  - 计算与真实细胞类型标签的一致性度量(NMI和ARI)

### 5.3 RGM特异性分析

```python
def plot_cell_type_specific_RGMs(self, cell_group, top_n=5, return_specific_RGMs=False,
                                save_path=None):
    """
    识别并可视化细胞类型特异的RGM
    """
    # 计算每个RGM在不同细胞类型中的平均活性
    auc_mtx = self.RGMs_AUCell_dict['aucell']
    cell_auc_mean = pd.DataFrame(index=auc_mtx.columns, columns=sorted(set(cell_group)))
  
    # 计算特异性分数
    for ct in set(cell_group):
        cell_auc_mean.loc[:, ct] = auc_mtx.loc[cell_group == ct, :].mean(axis=0)
  
    # 找出每个细胞类型特异的RGM
    specific_RGMs = {}
    for ct in cell_auc_mean.columns:
        temp = cell_auc_mean.copy()
        temp['spec_score'] = temp[ct] / (temp.drop(ct, axis=1).mean(axis=1) + 1e-9)
        temp = temp.sort_values('spec_score', ascending=False)
        specific_RGMs[ct] = list(temp.index[:top_n])
```

- **功能**：识别每种细胞类型特异表达的RGM
- **技术细节**：
  - 计算特异性分数：该类型中的活性/其他类型中的平均活性
  - 选择每种类型中特异性最高的前N个RGM
  - 可视化不同细胞类型的特征RGM

## 6. RGM的生物学解释

### 6.1 前馈调控(Out-RGM)

- **特点**：以驱动调控因子为起点，包含其直接调控的下游基因
- **生物学意义**：
  - 反映转录因子引起的基因表达级联效应
  - 表示特定细胞状态所激活的基因程序
  - 类似于经典的转录因子调控子(regulon)

### 6.2 反馈调控(In-RGM)

- **特点**：以反馈环路中的关键基因为中心，包含其上游调控者
- **生物学意义**：
  - 反映对驱动基因的协同调控
  - 代表维持细胞状态所需的调控环路
  - 揭示稳态机制或状态转变触发因素

### 6.3 应用价值

- **细胞类型标记**：特异性RGM可作为细胞类型标记
- **状态转换分析**：RGM活性变化揭示状态转换机制
- **关键通路识别**：高活性RGM反映活跃的生物学通路
- **潜在靶点识别**：高活性模块中的关键基因可作为治疗靶点

## 7. RGM活性分析的创新点

### 7.1 结合网络控制理论

- 使用MDS和MFVS的结果定义具有功能意义的基因模块
- 超越传统的共表达或基于相关性的模块识别方法

### 7.2 双向调控视角

- 同时考虑前馈和反馈两种调控模式
- 提供更全面的调控网络动态理解

### 7.3 整合差异表达信息

- 仅考虑差异表达基因作为RGM成员
- 增强生物学相关性和解释性

### 7.4 细胞分辨率分析

- 在单细胞水平评估调控模块活性
- 揭示细胞异质性和微环境影响

## 8. RGM活性分析流程总结

1. **RGM构建**：

   - 基于驱动调控因子定义前馈和反馈RGM
   - 筛选至少包含10个差异表达基因的模块
2. **活性评分**：

   - 使用AUCell算法计算每个细胞中RGM的活性
   - Z-score标准化使不同RGM可比较
3. **结果分析**：

   - 热图可视化RGM活性模式
   - 基于RGM活性进行细胞聚类
   - 识别细胞类型特异的RGM
4. **生物学解释**：

   - 前馈RGM反映转录级联效应
   - 反馈RGM表示调控环路和稳态机制
   - RGM活性变化揭示细胞状态转换机制

这种多层次的RGM活性分析方法提供了从网络结构到功能动态的桥梁，帮助研究者理解复杂的基因调控机制及其在细胞命运决定中的作用。

# 九、

# CEFCON模型整体解读

## 一、模型概述

CEFCON (Cell-linEage-speciFic gene regulatory netwOrk coNstruction) 是一个用于构建细胞谱系特异性基因调控网络的计算框架，它创新性地结合了图神经网络和网络控制理论，从单细胞RNA测序数据中揭示关键的基因调控机制。

## 二、整体架构与工作流程

CEFCON的工作流程分为五个主要阶段，形成一个完整的分析管道：

```
[输入数据] → [数据预处理] → [图神经网络] → [网络推断] → [驱动因子识别] → [RGM活性分析] → [生物学解释]
```

### 1. 数据预处理阶段

- **数据整合**：融合基因表达数据、先验网络和差异表达信息
- **基因筛选**：保留同时存在于表达数据和先验网络中的基因
- **网络增强**：通过相关性分析添加额外的高相关边
- **特征注释**：添加转录因子标记、网络中心性等特征

### 2. 图神经网络阶段

- **框架选择**：采用DeepGraphInfomax自监督学习框架
- **编码器结构**：双向图注意力网络，捕获基因间复杂交互
- **注意力机制**：支持多种注意力计算方式(COS、AD、SD)
- **训练策略**：多次运行取平均，提高结果稳定性
- **输出**：基因嵌入向量和边注意力权重

### 3. 网络推断阶段

- **权重缩放**：基于节点度的注意力权重全局缩放
- **边筛选**：应用阈值选择最显著的调控关系
- **稳定性过滤**：通过变异系数筛选稳定边
- **网络构建**：生成细胞谱系特异性的基因调控网络

### 4. 驱动调控因子识别阶段

- **影响分数**：计算综合考虑出入度的基因影响分数
- **网络控制方法**：结合MDS(最小支配集)和MFVS(最小反馈顶点集)
- **图约简**：高效图约简策略处理大规模网络
- **驱动因子**：识别控制网络动态的关键调控基因

### 5. RGM活性分析阶段

- **模块定义**：构建前馈(out)和反馈(in)两类调控模块
- **活性评分**：使用AUCell算法计算细胞中RGM活性
- **活性可视化**：热图展示不同细胞中RGM活性模式
- **功能解析**：揭示细胞状态与基因调控模块的关系

## 三、核心创新点

### 1. 多源数据整合

- 整合基因表达、先验网络和差异表达数据
- 结合先验知识和数据驱动的优势

### 2. 双向图注意力网络

- 同时考虑基因作为调控者和被调控者的角色
- 捕获复杂的调控关系和反馈环路

### 3. 网络控制理论应用

- 将MDS和MFVS方法应用于驱动调控因子识别
- 通过图约简提高计算效率和生物学解释性

### 4. RGM概念创新

- 提出类调控子基因模块(RGM)概念
- 区分前馈和反馈调控模式，全面描述调控机制

### 5. 细胞谱系特异性分析

- 针对特定细胞谱系构建调控网络
- 揭示发育过程中的调控动态变化

## 四、生物学意义

### 1. 调控网络揭示

- 识别细胞谱系特异的基因调控关系
- 量化调控强度，区分主要和次要调控

### 2. 关键调控因子

- 发现维持细胞状态的核心驱动基因
- 识别可能触发状态转变的调控开关

### 3. 调控模块动态

- 通过RGM活性揭示不同细胞状态的调控特征
- 识别细胞类型特异的基因表达程序

### 4. 调控层级与结构

- 揭示网络的层级结构和调控级联
- 识别反馈环路和调控枢纽

## 五、算法技术特点

### 1. 自监督学习架构

- 不依赖标记数据，适用于探索性分析
- 通过对比学习捕获表达数据中的结构信息

### 2. 多层次注意力机制

- 第一层捕获直接交互，第二层捕获间接关系
- 通过参数miu灵活调整不同层的重要性

### 3. 边权重缩放策略

- 结合局部注意力和全局拓扑特性
- 考虑节点度重要性，平衡hub节点影响

### 4. 整数线性规划优化

- 高效求解网络控制问题
- 多种图约简策略降低复杂度

### 5. 多重验证机制

- 多次运行取平均增强稳定性
- 变异系数过滤减少噪声影响

## 六、应用价值

### 1. 发育生物学研究

- 揭示细胞分化过程中的调控变化
- 识别细胞命运决定的关键因子

### 2. 疾病机制研究

- 发现疾病相关的调控网络扰动
- 识别潜在的治疗靶点

### 3. 细胞类型标记

- 基于RGM活性特征识别细胞类型
- 发现新的细胞类型标记物

### 4. 转录调控预测

- 预测基因间的调控关系强度
- 补充现有的调控数据库

## 七、模型优势与独特性

1. **整合优势**: 结合深度学习与网络科学方法
2. **多视角**: 同时考虑前馈调控和反馈环路
3. **可扩展性**: 适用于不同类型的单细胞数据
4. **可解释性**: 提供生物学可解释的结果
5. **计算效率**: 通过多种策略提高大规模网络处理效率

CEFCON通过创新性地结合图神经网络和网络控制理论，从单细胞RNA测序数据中构建细胞谱系特异的基因调控网络，并识别关键驱动调控因子，为理解基因调控机制和细胞命运决定提供了有力工具，在发育生物学、疾病研究和精准医疗等领域具有广阔的应用前景。

# CEFCON模型详细介绍及应用价值

## 一、CEFCON模型概述

CEFCON (Cell-linEage-speciFic gene regulatory netwOrk coNstruction) 是一个用于构建细胞谱系特异性基因调控网络的计算框架。它创新性地结合了图神经网络和网络控制理论，能够从单细胞RNA测序(scRNA-seq)数据中揭示细胞发育和分化过程中的关键基因调控机制。

## 二、CEFCON的技术架构

CEFCON的完整分析流程包含五个主要阶段：

### 1. 数据预处理与整合

- 整合三类数据：基因表达数据、先验基因交互网络、差异表达信息
- 筛选共有基因，标注转录因子，计算网络中心性指标
- 基于表达相关性添加高置信度额外连接

### 2. 图神经网络学习

- 使用双向图注意力网络捕获基因间的复杂调控关系
- 通过DeepGraphInfomax自监督学习框架训练网络
- 支持多种注意力机制(余弦相似度、加性、缩放点积)
- 多次运行取平均，确保结果稳定性

### 3. 网络推断与构建

- 基于学习到的注意力权重推断调控关系强度
- 应用节点度缩放策略，平衡局部与全局网络特征
- 通过阈值筛选和变异系数过滤，构建高置信度调控网络

### 4. 驱动调控因子识别

- 计算考虑出入度的基因影响分数
- 结合MDS(最小支配集)和MFVS(最小反馈顶点集)网络控制方法
- 使用高效图约简策略处理大规模网络
- 识别控制网络动态的关键调控基因

### 5. RGM活性分析

- 构建前馈(out)和反馈(in)两类调控基因模块(RGM)
- 使用AUCell算法量化各细胞中RGM的活性水平
- 分析RGM活性与细胞状态、类型的关联
- 识别细胞类型特异的调控模块

## 三、CEFCON的技术创新点

1. **双向图注意力机制**：同时考虑基因作为调控者和被调控者的双重角色
2. **网络控制理论应用**：将复杂网络控制理论引入调控网络分析
3. **RGM概念创新**：提出新型调控模块定义，区分前馈和反馈调控
4. **细胞谱系特异性**：针对特定发育路径构建精确调控网络
5. **多层次整合**：从网络推断到功能解析的完整分析管道

## 四、CEFCON的应用价值

### 1. 发育与分化研究

- **发育轨迹解析**：揭示细胞从干/祖细胞到分化细胞过程中的调控变化
- **命运决定研究**：识别细胞命运决定的关键调控因子和调控事件
- **谱系分支机制**：阐明不同细胞分支形成的分子调控机制

### 2. 疾病与医学研究

- **疾病机制探索**：揭示疾病状态下的调控网络异常
- **药物靶点识别**：发现可干预的关键调控节点作为潜在治疗靶点
- **生物标志物发现**：识别疾病特异的RGM作为新型生物标志物

### 3. 细胞类型与功能注释

- **细胞类型标记**：基于RGM活性特征识别和定义细胞类型
- **功能推断**：通过调控网络结构推断未知细胞类型的功能
- **稀有亚群发现**：识别具有特殊调控特征的罕见细胞亚群

### 4. 转录调控机制研究

- **调控层级解析**：揭示基因调控的层级结构和信息流动
- **反馈环路鉴定**：发现维持细胞状态的关键反馈机制
- **调控协同分析**：研究多个转录因子如何协同调控目标基因

### 5. 干细胞与再生医学

- **细胞重编程**：识别细胞重编程的关键调控因子
- **分化诱导优化**：基于调控网络优化细胞分化方案
- **器官发生模型**：构建器官发育的调控网络模型

## 五、与传统方法相比的优势

1. **整合深度学习与网络科学**：超越简单的相关性或回归方法
2. **调控强度量化**：提供基因间调控关系的定量评估
3. **双向调控视角**：同时考虑前馈调控和反馈环路
4. **高计算效率**：通过图约简等策略高效处理大规模网络
5. **良好可解释性**：提供生物学可理解的调控关系和模块

## 六、实际应用案例

1. **造血系统分化**：解析从造血干细胞到各血液细胞类型的调控网络变化
2. **神经发育**：揭示神经元分化过程中的关键调控因子
3. **肿瘤异质性**：分析肿瘤内不同细胞亚群的调控差异
4. **免疫细胞功能**：阐明免疫细胞激活过程中的调控重编程

## 七、总结

CEFCON通过创新性地结合图神经网络和网络控制理论，提供了从单细胞RNA测序数据构建细胞谱系特异性基因调控网络的强大工具。它不仅能够识别关键驱动调控因子，还能揭示调控模块的动态活性变化，为理解细胞分化、发育和疾病过程中的基因调控机制提供了新视角。

这一方法体系在基础生物学研究、疾病机制探索、精准医疗和药物研发等领域具有广泛的应用前景，有望成为单细胞转录组学研究中的重要分析工具。

细胞谱系(Cell Lineage)在CEFCON模型中是一个核心概念，它具有以下含义：

## 细胞谱系的基本定义

细胞谱系指的是从干细胞或祖细胞到特定分化细胞的发育路径或分化轨迹。它描述了细胞如何通过一系列分裂和分化事件，最终形成具有特定功能的成熟细胞类型。

## 在CEFCON中的应用

1. **发育路径特异性**：CEFCON不是构建通用的基因调控网络，而是针对特定发育路径的网络，例如从造血干细胞到B细胞的谱系，或从上皮干细胞到角质细胞的谱系。
2. **时间序列概念**：细胞谱系包含时间维度，反映细胞随时间变化的分化状态。CEFCON可以分析这种时间序列过程中的调控变化。
3. **拟时间分析**：在单细胞数据中，通常使用"拟时间"(pseudotime)分析重建细胞谱系，根据基因表达相似性排列细胞，推断发育轨迹。
4. **分支结构**：细胞谱系往往呈现树状分支结构，一个祖细胞可以分化为多种不同的细胞类型。CEFCON能够分析特定分支的调控特点。

## 生物学意义

1. **命运决定**：细胞谱系分析帮助理解决定细胞命运的关键调控事件。
2. **发育障碍**：分析特定谱系有助于发现发育障碍或疾病的分子机制。
3. **细胞重编程**：了解谱系特异的调控网络有助于优化细胞重编程策略。
4. **异质性理解**：揭示同一组织中不同细胞谱系的调控差异，解释细胞异质性。

CEFCON通过分析特定细胞谱系的基因调控网络，揭示了不同发育路径特有的调控机制，这比传统的混合所有细胞类型的分析方法提供了更精确、更有针对性的生物学见解。
