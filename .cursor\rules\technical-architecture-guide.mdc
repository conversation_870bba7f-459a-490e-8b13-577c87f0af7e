---
description: 
globs: 
alwaysApply: false
---
# Technical Architecture Guide

## Core Components by Tool

### CEFCON Architecture
**Main Pipeline**: [CEFCON.py](mdc:multi_model/cefcon/CEFCON.py) → [cell_lineage_GRN.py](mdc:multi_model/cefcon/cell_lineage_GRN.py) → [driver_regulators.py](mdc:multi_model/cefcon/driver_regulators.py)

- **Data Preparation**: [utils.py](mdc:multi_model/cefcon/utils.py) - Handles input processing and network construction
- **Model Training**: `NetModel` class in [cell_lineage_GRN.py](mdc:multi_model/cefcon/cell_lineage_GRN.py)
- **Network Analysis**: `CefconResults` class in [cefcon_result_object.py](mdc:multi_model/cefcon/cefcon_result_object.py)
- **Evaluation**: [eval_utils.py](mdc:multi_model/cefcon/eval_utils.py) - Performance metrics and validation

**Key Models**:
- `GraphAttention_layer`: Multi-head attention for gene interactions
- `GRN_Encoder`: Graph neural network encoder with BatchNorm
- `NetModel`: Main training orchestrator with Deep Graph Infomax

### expiMap Architecture  
**Main Classes**: [expimap_model.py](mdc:multi_model/expimap/expimap_model.py) contains `EXPIMAP` wrapper, [expimap.py](mdc:multi_model/expimap/expimap.py) has core `expiMap` model

- **Model Core**: [CVAELatentsModelMixin.py](mdc:multi_model/expimap/CVAELatentsModelMixin.py) - Base VAE functionality
- **Masked Layers**: [modules.py](mdc:multi_model/expimap/modules.py) - `MaskedLinear`, `MaskedCondLayers`
- **Training**: [regularized.py](mdc:multi_model/expimap/regularized.py) - `expiMapTrainer` with group lasso regularization
- **Loss Functions**: [losses.py](mdc:multi_model/expimap/losses.py), [trvae_losses.py](mdc:multi_model/expimap/trvae_losses.py)

**Key Features**:
- Pathway-guided masking for interpretability
- HSIC regularization for latent disentanglement
- Group lasso for sparse gene selection

### scGNN Architecture
**Main Training**: [scGNN.py](mdc:multi_model/scGNN/scGNN.py) - Contains main training loop and VAE implementation

- **Graph Functions**: [graph_function.py](mdc:multi_model/scGNN/graph_function.py) - KNN graph construction and distance calculations
- **Preprocessing**: [PreprocessingscGNN.py](mdc:multi_model/scGNN/PreprocessingscGNN.py) - Data loading and filtering
- **Utilities**: [util_function.py](mdc:multi_model/scGNN/util_function.py) - Dataset classes and loss functions
- **GAE Module**: [gae/](mdc:multi_model/scGNN/gae) - Graph autoencoder implementation
- **Benchmarking**: [benchmark_util.py](mdc:multi_model/scGNN/benchmark_util.py) - Evaluation metrics

**Key Models**:
- VAE with graph regularization
- KNN graph construction with multiple distance metrics
- LTMG (Left Truncated Mixed Gaussian) for discretization

### scNET Architecture
**Main Entry**: [main.py](mdc:multi_model/scNET/main.py) contains `run_scNET` function and training pipeline

- **Multi-Graph Model**: [MultyGraphModel.py](mdc:multi_model/scNET/MultyGraphModel.py) - Core neural architectures
- **Data Handling**: [KNNDataset.py](mdc:multi_model/scNET/KNNDataset.py) - Custom dataset classes
- **Network Analysis**: [coEmbeddedNetwork.py](mdc:multi_model/scNET/coEmbeddedNetwork.py) - Downstream analysis functions
- **Utilities**: [Utils.py](mdc:multi_model/scNET/Utils.py) - Helper functions for propagation and evaluation

**Key Models**:
- `MutualEncoder`: Handles both KNN and PPI graph information
- `scNET`: Main model combining multiple encoders
- `TransformerConvReducrLayer`: Custom transformer convolution

## Common Patterns

### Data Flow Architecture
1. **Input Processing**: CSV/H5AD → AnnData → Tensor format
2. **Graph Construction**: Feature matrix → KNN/PPI graphs → Edge indices
3. **Model Training**: Graph + Expression → Latent embeddings → Reconstructed data
4. **Downstream Analysis**: Embeddings → Clustering/Trajectory/Network inference

### Shared Dependencies
- **Deep Learning**: PyTorch, PyTorch Geometric
- **Single-cell**: Scanpy, AnnData  
- **Graph Analysis**: NetworkX
- **Optimization**: CVXPY (CEFCON), custom optimizers
- **R Integration**: rpy2, R scripts for MAST/Slingshot

### Integration Points
- **Preprocessing**: Unify data loading from [utils.py](mdc:multi_model/cefcon/utils.py), [PreprocessingscGNN.py](mdc:multi_model/scGNN/PreprocessingscGNN.py)
- **Graph Construction**: Merge approaches from [graph_function.py](mdc:multi_model/scGNN/graph_function.py), [Utils.py](mdc:multi_model/scNET/Utils.py)
- **Model Components**: Combine attention mechanisms, VAE architectures, regularization strategies
- **Evaluation**: Standardize metrics from [eval_utils.py](mdc:multi_model/cefcon/eval_utils.py), [benchmark_util.py](mdc:multi_model/scGNN/benchmark_util.py)
