---
description: 
globs: 
alwaysApply: false
---
# Single-Cell Analysis Tools Integration Project

## Project Overview
This workspace contains multiple open-source single-cell analysis tools that are being integrated into a unified algorithm library. The project aims to combine the strengths of different approaches for comprehensive single-cell data analysis.

## Main Project Structure
- `multi_model/` - Contains all single-cell analysis tools

## Single-Cell Tools Overview

### Core Analysis Tools
1. **CEFCON** - [multi_model/cefcon/](mdc:multi_model/cefcon)
   - Cell lineage-specific Gene Regulatory Network construction
   - Key files: [CEFCON.py](mdc:multi_model/cefcon/CEFCON.py), [cell_lineage_GRN.py](mdc:multi_model/cefcon/cell_lineage_GRN.py)
   - Focus: Driver regulator identification and regulatory module analysis

2. **expiMap** - [multi_model/expimap/](mdc:multi_model/expimap)
   - Explainable masked variational autoencoder
   - Key files: [expimap_model.py](mdc:multi_model/expimap/expimap_model.py), [expimap.py](mdc:multi_model/expimap/expimap.py)
   - Focus: Interpretable pathway-level analysis

3. **scGNN** - [multi_model/scGNN/](mdc:multi_model/scGNN)
   - Graph Neural Network for single-cell analysis
   - Key files: [scGNN.py](mdc:multi_model/scGNN/scGNN.py), [graph_function.py](mdc:multi_model/scGNN/graph_function.py)
   - Focus: Graph-based representation learning and imputation

4. **scNET** - [multi_model/scNET/](mdc:multi_model/scNET)
   - Multi-graph co-embedding network
   - Key files: [main.py](mdc:multi_model/scNET/main.py), [MultyGraphModel.py](mdc:multi_model/scNET/MultyGraphModel.py)
   - Focus: Joint cell-gene embedding and network analysis

### Analysis Focus Areas
- **Graph Neural Networks**: scGNN, scNET
- **Variational Autoencoders**: expiMap, 
- **Network Inference**: CEFCON, 


## Integration Objectives
- Combine preprocessing pipelines from multiple tools
- Merge complementary algorithmic approaches (GNN + VAE)
- Create unified API for single-cell analysis workflows
- Implement cross-language bridges (Python + R tools)

## Development Guidelines
- Focus on PyTorch as primary deep learning backend
- Maintain compatibility with standard formats (H5AD, CSV, H5)
- Implement scalable solutions for large datasets (>1M cells)
- Preserve interpretability features from individual tools
