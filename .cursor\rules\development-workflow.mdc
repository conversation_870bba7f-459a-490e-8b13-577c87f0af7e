---
description: 
globs: 
alwaysApply: false
---
# Development Workflow & Requirements

## Requirements Gathering System
This project uses the Claude Requirements Gathering System located in [claude-code-requirements-builder/](mdc:claude-code-requirements-builder).

### Key Commands
- **Start Requirements**: Use [requirements-start.md](mdc:claude-code-requirements-builder/commands/requirements-start.md) to begin new feature development
- **Check Status**: [requirements-status.md](mdc:claude-code-requirements-builder/commands/requirements-status.md) to view current progress
- **View Details**: [requirements-current.md](mdc:claude-code-requirements-builder/commands/requirements-current.md) for active requirements
- **List All**: [requirements-list.md](mdc:claude-code-requirements-builder/commands/requirements-list.md) to see all requirements and their status

### Requirements Storage
- **Index**: [requirements/index.md](mdc:claude-code-requirements-builder/requirements/index.md) - Central requirements registry
- **Active Tracking**: `.current-requirement` file tracks ongoing work
- **Individual Requirements**: Stored in timestamped folders with structured files

## Integration Development Strategy

### Phase 1: Foundation (Data Pipeline)
Focus on unifying data preprocessing and input handling:
- Standardize input formats across all tools
- Create unified data preparation functions
- Implement consistent quality control and filtering
- Establish common graph construction methods

### Phase 2: Core Models (Algorithm Integration)  
Merge complementary algorithmic approaches:
- Combine GNN architectures from scGNN and scNET
- Integrate VAE approaches from expiMap and CEFCON
- Implement hybrid attention mechanisms
- Create modular model components for reuse

### Phase 3: Analysis Pipeline (Downstream Tasks)
Unify analysis and interpretation capabilities:
- Standardize clustering and cell type annotation
- Integrate trajectory inference methods
- Combine network inference approaches
- Implement comprehensive visualization suite

### Phase 4: Optimization (Performance & Scalability)
Enhance computational efficiency:
- Optimize memory usage for large datasets
- Implement distributed training capabilities
- Add GPU acceleration for all components
- Create efficient data loading and batching

## Development Guidelines

### Code Organization
```
unified_sctools/
├── core/                 # Core algorithm modules
│   ├── models/           # Neural network architectures
│   ├── layers/           # Custom layer implementations
│   └── losses/           # Loss functions and regularizers
├── preprocessing/        # Data preparation pipeline
├── analysis/            # Downstream analysis functions
├── utils/               # Utility functions and helpers
├── evaluation/          # Benchmarking and metrics
└── examples/            # Usage examples and tutorials
```

### Integration Priorities
1. **High Priority**: Data preprocessing, basic VAE/GNN models
2. **Medium Priority**: Advanced regularization, multi-graph handling
3. **Low Priority**: Specialized visualization, R language bridges

### Testing Strategy
- Unit tests for individual model components
- Integration tests for data pipelines
- Benchmark tests against original tool performance
- Memory and speed profiling for large datasets

### Documentation Requirements
- API documentation for all public functions
- Architecture diagrams for model components
- Usage tutorials with example datasets
- Performance benchmarks and comparisons

## Code Quality Standards
- Follow PEP 8 for Python code formatting
- Use type hints for all function signatures  
- Implement comprehensive error handling
- Add docstrings following NumPy style
- Maintain backwards compatibility with original tools

## Version Control Strategy
- Feature branches for individual tool integration
- Pull request reviews for all changes
- Semantic versioning for releases
- Tag major integration milestones
