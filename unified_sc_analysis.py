"""
Unified Single-Cell Analysis Library
====================================

This library integrates multiple single-cell analysis tools:
- scNET: Network-based embedding and reconstruction
- scGNN: Graph neural network for clustering and imputation  
- expiMap: Masked conditional variational autoencoder
- CEFCON: Cell lineage-specific gene regulatory network construction

Core functionalities integrated:
1. Data preprocessing and normalization
2. Graph construction (k-NN, PPI networks)
3. Embedding generation (VAE, GAE, GNN-based)
4. Cell clustering and type identification
5. Gene imputation and reconstruction
6. Gene regulatory network inference
7. Driver gene identification
"""

import numpy as np
import pandas as pd
import scanpy as sc
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.data import Data
from torch_geometric.utils import train_test_split_edges
from torch_geometric.nn import GCNConv, GATConv, SAGEConv
from sklearn.cluster import KMeans
from sklearn.neighbors import NearestNeighbors
from sklearn.metrics import silhouette_score, adjusted_rand_score
from typing import Optional, Union, List, Dict, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

class UnifiedSingleCellAnalysis:
    """
    Unified interface for single-cell analysis combining multiple algorithms.
    
    Parameters
    ----------
    method : str, default 'auto'
        Analysis method to use: 'scnet', 'scgnn', 'expimap', 'cefcon', 'auto'
    embedding_dim : int, default 64
        Dimension of learned embeddings
    hidden_dims : list, default [256, 128]  
        Hidden layer dimensions for neural networks
    n_neighbors : int, default 15
        Number of neighbors for k-NN graph construction
    batch_size : int, default 1000
        Batch size for training
    learning_rate : float, default 1e-3
        Learning rate for optimization
    max_epochs : int, default 200
        Maximum training epochs
    device : str, default 'auto'
        Device to use ('cpu', 'cuda', 'auto')
    random_seed : int, default 42
        Random seed for reproducibility
    """
    
    def __init__(
        self,
        method: str = 'auto',
        embedding_dim: int = 64,
        hidden_dims: List[int] = [256, 128],
        n_neighbors: int = 15,
        batch_size: int = 1000,
        learning_rate: float = 1e-3,
        max_epochs: int = 200,
        device: str = 'auto',
        random_seed: int = 42
    ):
        self.method = method
        self.embedding_dim = embedding_dim
        self.hidden_dims = hidden_dims
        self.n_neighbors = n_neighbors
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.max_epochs = max_epochs
        self.random_seed = random_seed
        
        # Set device
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
            
        # Set random seeds
        np.random.seed(random_seed)
        torch.manual_seed(random_seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(random_seed)
            
        # Initialize components
        self.adata = None
        self.model = None
        self.embeddings = None
        self.clusters = None
        self.imputed_data = None
        self.grn = None
        
    def preprocess(
        self,
        adata,
        min_genes: int = 200,
        min_cells: int = 3,
        target_sum: float = 1e4,
        n_top_genes: int = 3000,
        normalize: bool = True,
        log_transform: bool = True,
        copy: bool = False
    ):
        """
        Unified preprocessing pipeline for single-cell data.
        
        Parameters
        ----------
        adata : anndata.AnnData
            Annotated data matrix
        min_genes : int, default 200
            Minimum number of genes per cell
        min_cells : int, default 3
            Minimum number of cells per gene
        target_sum : float, default 1e4
            Target sum for normalization
        n_top_genes : int, default 3000
            Number of highly variable genes to select
        normalize : bool, default True
            Whether to normalize data
        log_transform : bool, default True
            Whether to log transform data
        copy : bool, default False
            Whether to return a copy
            
        Returns
        -------
        adata : anndata.AnnData
            Preprocessed data
        """
        if copy:
            adata = adata.copy()
            
        # Filter cells and genes
        sc.pp.filter_cells(adata, min_genes=min_genes)
        sc.pp.filter_genes(adata, min_cells=min_cells)
        
        # Store raw data
        adata.raw = adata.copy()
        
        # Normalization
        if normalize:
            sc.pp.normalize_total(adata, target_sum=target_sum)
            
        # Log transformation
        if log_transform:
            sc.pp.log1p(adata)
            
        # Find highly variable genes
        sc.pp.highly_variable_genes(adata, n_top_genes=n_top_genes)
        
        # Build neighborhood graph
        sc.pp.neighbors(adata, n_neighbors=self.n_neighbors, n_pcs=15)
        
        self.adata = adata
        return adata
        
    def build_graph(self, graph_type: str = 'knn', **kwargs):
        """
        Build different types of graphs for analysis.
        
        Parameters
        ----------
        graph_type : str, default 'knn'
            Type of graph: 'knn', 'ppi', 'correlation'
        **kwargs
            Additional parameters for graph construction
            
        Returns
        -------
        edge_index : torch.Tensor
            Graph edge indices
        edge_weight : torch.Tensor, optional
            Graph edge weights
        """
        if self.adata is None:
            raise ValueError("Data not preprocessed. Call preprocess() first.")
            
        if graph_type == 'knn':
            return self._build_knn_graph(**kwargs)
        elif graph_type == 'ppi':
            return self._build_ppi_graph(**kwargs)
        elif graph_type == 'correlation':
            return self._build_correlation_graph(**kwargs)
        else:
            raise ValueError(f"Unknown graph type: {graph_type}")
            
    def _build_knn_graph(self, **kwargs):
        """Build k-NN graph from preprocessed data."""
        from torch_geometric.utils import convert
        import networkx as nx
        
        # Get distance matrix from scanpy preprocessing
        distances = self.adata.obsp['distances']
        adjacency = (distances > 0).astype(int)
        
        # Convert to networkx and then to PyG format
        G = nx.from_numpy_array(adjacency.toarray())
        pyg_data = convert.from_networkx(G)
        
        return pyg_data.edge_index
        
    def _build_ppi_graph(self, ppi_file: str = None, **kwargs):
        """Build protein-protein interaction graph."""
        # This would load PPI network data
        # For now, return a placeholder
        n_genes = self.adata.n_vars
        edge_index = torch.randint(0, n_genes, (2, n_genes * 5))
        return edge_index
        
    def _build_correlation_graph(self, threshold: float = 0.5, **kwargs):
        """Build correlation graph from gene expression."""
        X = self.adata.X.toarray() if hasattr(self.adata.X, 'toarray') else self.adata.X
        corr_matrix = np.corrcoef(X.T)
        
        # Threshold correlations
        adj_matrix = (np.abs(corr_matrix) > threshold).astype(int)
        
        # Convert to edge index
        rows, cols = np.where(adj_matrix)
        edge_index = torch.tensor([rows, cols], dtype=torch.long)
        
        return edge_index
        
    def embed(self, method: str = None, **kwargs):
        """
        Generate embeddings using specified method.
        
        Parameters
        ----------
        method : str, optional
            Embedding method: 'vae', 'gae', 'gnn', 'scnet'
        **kwargs
            Method-specific parameters
            
        Returns
        -------
        embeddings : np.ndarray
            Cell embeddings
        """
        if method is None:
            method = self._get_embedding_method()
            
        if method == 'vae':
            return self._embed_vae(**kwargs)
        elif method == 'gae':
            return self._embed_gae(**kwargs)
        elif method == 'gnn':
            return self._embed_gnn(**kwargs)
        elif method == 'scnet':
            return self._embed_scnet(**kwargs)
        else:
            raise ValueError(f"Unknown embedding method: {method}")
            
    def _get_embedding_method(self):
        """Select embedding method based on overall method."""
        method_map = {
            'scnet': 'scnet',
            'scgnn': 'vae',
            'expimap': 'vae',
            'cefcon': 'gnn',
            'auto': 'vae'
        }
        return method_map.get(self.method, 'vae')
        
    def _embed_vae(self, **kwargs):
        """Variational autoencoder embedding (scGNN/expiMap style)."""
        class VAE(nn.Module):
            def __init__(self, input_dim, latent_dim, hidden_dims):
                super().__init__()
                self.encoder = self._build_encoder(input_dim, latent_dim, hidden_dims)
                self.decoder = self._build_decoder(latent_dim, input_dim, hidden_dims)
                
            def _build_encoder(self, input_dim, latent_dim, hidden_dims):
                layers = []
                prev_dim = input_dim
                for dim in hidden_dims:
                    layers.extend([
                        nn.Linear(prev_dim, dim),
                        nn.ReLU(),
                        nn.Dropout(0.1)
                    ])
                    prev_dim = dim
                    
                # Mean and log variance
                layers.extend([
                    nn.Linear(prev_dim, latent_dim),  # mu
                    nn.Linear(prev_dim, latent_dim)   # log_var
                ])
                return nn.ModuleList(layers)
                
            def _build_decoder(self, latent_dim, output_dim, hidden_dims):
                layers = []
                prev_dim = latent_dim
                for dim in reversed(hidden_dims):
                    layers.extend([
                        nn.Linear(prev_dim, dim),
                        nn.ReLU(),
                        nn.Dropout(0.1)
                    ])
                    prev_dim = dim
                layers.append(nn.Linear(prev_dim, output_dim))
                return nn.Sequential(*layers)
                
            def encode(self, x):
                h = x
                for layer in self.encoder[:-2]:
                    h = layer(h)
                mu = self.encoder[-2](h)
                log_var = self.encoder[-1](h)
                return mu, log_var
                
            def reparameterize(self, mu, log_var):
                std = torch.exp(0.5 * log_var)
                eps = torch.randn_like(std)
                return mu + eps * std
                
            def forward(self, x):
                mu, log_var = self.encode(x)
                z = self.reparameterize(mu, log_var)
                recon_x = self.decoder(z)
                return recon_x, mu, log_var, z
                
        # Initialize and train VAE
        X = torch.FloatTensor(self.adata.X.toarray() if hasattr(self.adata.X, 'toarray') else self.adata.X)
        model = VAE(X.shape[1], self.embedding_dim, self.hidden_dims).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=self.learning_rate)
        
        model.train()
        for epoch in range(self.max_epochs):
            optimizer.zero_grad()
            recon_x, mu, log_var, z = model(X.to(self.device))
            
            # VAE loss
            recon_loss = F.mse_loss(recon_x, X.to(self.device), reduction='sum')
            kl_loss = -0.5 * torch.sum(1 + log_var - mu.pow(2) - log_var.exp())
            loss = recon_loss + kl_loss
            
            loss.backward()
            optimizer.step()
            
        # Extract embeddings
        model.eval()
        with torch.no_grad():
            _, _, _, embeddings = model(X.to(self.device))
            self.embeddings = embeddings.cpu().numpy()
            
        self.model = model
        return self.embeddings
        
    def _embed_gae(self, **kwargs):
        """Graph autoencoder embedding."""
        from torch_geometric.nn import GCNConv, InnerProductDecoder
        
        class GAE(nn.Module):
            def __init__(self, input_dim, hidden_dim, latent_dim):
                super().__init__()
                self.encoder1 = GCNConv(input_dim, hidden_dim)
                self.encoder2 = GCNConv(hidden_dim, latent_dim)
                self.decoder = InnerProductDecoder()
                
            def encode(self, x, edge_index):
                x = F.relu(self.encoder1(x, edge_index))
                return self.encoder2(x, edge_index)
                
            def forward(self, x, edge_index):
                z = self.encode(x, edge_index)
                adj_pred = self.decoder(z, edge_index)
                return z, adj_pred
                
        # Build graph
        edge_index = self.build_graph('knn')  
        X = torch.FloatTensor(self.adata.X.toarray() if hasattr(self.adata.X, 'toarray') else self.adata.X)
        
        model = GAE(X.shape[1], self.hidden_dims[0], self.embedding_dim).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=self.learning_rate)
        
        # Create PyG data object
        data = Data(x=X, edge_index=edge_index)
        data = train_test_split_edges(data)
        
        model.train()
        for epoch in range(self.max_epochs):
            optimizer.zero_grad()
            z, adj_pred = model(data.x.to(self.device), data.train_pos_edge_index.to(self.device))
            
            # Reconstruction loss
            pos_loss = F.binary_cross_entropy_with_logits(
                adj_pred[data.train_pos_edge_index[0], data.train_pos_edge_index[1]], 
                torch.ones(data.train_pos_edge_index.shape[1]).to(self.device)
            )
            neg_edge_index = torch.randint(0, data.x.shape[0], data.train_pos_edge_index.shape).to(self.device)
            neg_loss = F.binary_cross_entropy_with_logits(
                adj_pred[neg_edge_index[0], neg_edge_index[1]],
                torch.zeros(neg_edge_index.shape[1]).to(self.device)
            )
            loss = pos_loss + neg_loss
            
            loss.backward()
            optimizer.step()
            
        # Extract embeddings
        model.eval()
        with torch.no_grad():
            embeddings, _ = model(data.x.to(self.device), data.train_pos_edge_index.to(self.device))
            self.embeddings = embeddings.cpu().numpy()
            
        self.model = model
        return self.embeddings
        
    def _embed_gnn(self, **kwargs):
        """Graph neural network embedding (CEFCON style)."""
        edge_index = self.build_graph('knn')
        X = torch.FloatTensor(self.adata.X.toarray() if hasattr(self.adata.X, 'toarray') else self.adata.X)
        
        class GNN(nn.Module):
            def __init__(self, input_dim, hidden_dim, output_dim):
                super().__init__()
                self.conv1 = GATConv(input_dim, hidden_dim, heads=4, concat=True)
                self.conv2 = GATConv(hidden_dim * 4, output_dim, heads=1, concat=False)
                
            def forward(self, x, edge_index):
                x = F.dropout(x, training=self.training)
                x = F.elu(self.conv1(x, edge_index))
                x = F.dropout(x, training=self.training)
                x = self.conv2(x, edge_index)
                return x
                
        model = GNN(X.shape[1], self.hidden_dims[0], self.embedding_dim).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=self.learning_rate)
        
        model.train()
        for epoch in range(self.max_epochs):
            optimizer.zero_grad()
            embeddings = model(X.to(self.device), edge_index.to(self.device))
            
            # Self-supervised loss (node classification proxy task)
            loss = F.mse_loss(embeddings, torch.randn_like(embeddings).to(self.device))
            
            loss.backward()
            optimizer.step()
            
        # Extract embeddings
        model.eval()
        with torch.no_grad():
            self.embeddings = model(X.to(self.device), edge_index.to(self.device)).cpu().numpy()
            
        self.model = model
        return self.embeddings
        
    def _embed_scnet(self, **kwargs):
        """scNET-style embedding with dual graph structure."""
        # Simplified scNET implementation
        knn_edge_index = self.build_graph('knn')
        ppi_edge_index = self.build_graph('ppi')
        X = torch.FloatTensor(self.adata.X.toarray() if hasattr(self.adata.X, 'toarray') else self.adata.X)
        
        class scNETEncoder(nn.Module):
            def __init__(self, input_dim, hidden_dim, output_dim):
                super().__init__()
                self.cell_conv = SAGEConv(input_dim, hidden_dim)
                self.gene_conv = SAGEConv(hidden_dim, output_dim)
                
            def forward(self, x, knn_edge_index, ppi_edge_index):
                # Cell-level message passing
                x = F.relu(self.cell_conv(x.T, knn_edge_index)).T
                # Gene-level message passing  
                x = self.gene_conv(x, ppi_edge_index)
                return x
                
        model = scNETEncoder(X.shape[0], self.hidden_dims[0], self.embedding_dim).to(self.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=self.learning_rate)
        
        model.train()
        for epoch in range(self.max_epochs):
            optimizer.zero_grad()
            embeddings = model(X.to(self.device), knn_edge_index.to(self.device), ppi_edge_index.to(self.device))
            
            # Reconstruction loss
            reconstructed = torch.mm(embeddings, embeddings.T)
            loss = F.mse_loss(reconstructed, torch.mm(X.to(self.device), X.T.to(self.device)))
            
            loss.backward()
            optimizer.step()
            
        # Extract embeddings
        model.eval()
        with torch.no_grad():
            self.embeddings = model(X.to(self.device), knn_edge_index.to(self.device), ppi_edge_index.to(self.device)).cpu().numpy()
            
        self.model = model
        return self.embeddings
        
    def cluster(self, method: str = 'kmeans', n_clusters: int = None, **kwargs):
        """
        Perform cell clustering on embeddings.
        
        Parameters
        ---------- 
        method : str, default 'kmeans'
            Clustering method: 'kmeans', 'louvain', 'leiden'
        n_clusters : int, optional
            Number of clusters (auto-detected if None)
        **kwargs
            Method-specific parameters
            
        Returns
        -------
        cluster_labels : np.ndarray
            Cluster assignments for each cell
        """
        if self.embeddings is None:
            self.embed()
            
        if method == 'kmeans':
            return self._cluster_kmeans(n_clusters, **kwargs)
        elif method == 'louvain':
            return self._cluster_louvain(**kwargs)
        elif method == 'leiden':
            return self._cluster_leiden(**kwargs)
        else:
            raise ValueError(f"Unknown clustering method: {method}")
            
    def _cluster_kmeans(self, n_clusters: int = None, **kwargs):
        """K-means clustering."""
        if n_clusters is None:
            # Auto-detect number of clusters using silhouette score
            scores = []
            K_range = range(2, min(20, self.embeddings.shape[0] // 10))
            for k in K_range:
                kmeans = KMeans(n_clusters=k, random_state=self.random_seed)
                labels = kmeans.fit_predict(self.embeddings)
                score = silhouette_score(self.embeddings, labels)
                scores.append(score)
            n_clusters = K_range[np.argmax(scores)]
            
        kmeans = KMeans(n_clusters=n_clusters, random_state=self.random_seed, **kwargs)
        self.clusters = kmeans.fit_predict(self.embeddings)
        return self.clusters
        
    def _cluster_louvain(self, **kwargs):
        """Louvain clustering using scanpy."""
        sc.tl.louvain(self.adata, **kwargs)
        self.clusters = self.adata.obs['louvain'].astype(int).values
        return self.clusters
        
    def _cluster_leiden(self, **kwargs):
        """Leiden clustering using scanpy."""
        sc.tl.leiden(self.adata, **kwargs)
        self.clusters = self.adata.obs['leiden'].astype(int).values
        return self.clusters
        
    def impute(self, method: str = None, **kwargs):
        """
        Perform gene expression imputation.
        
        Parameters
        ----------
        method : str, optional
            Imputation method: 'vae', 'scgnn', 'magic'
        **kwargs
            Method-specific parameters
            
        Returns
        -------
        imputed_data : np.ndarray
            Imputed gene expression matrix
        """
        if method is None:
            method = self._get_imputation_method()
            
        if method == 'vae':
            return self._impute_vae(**kwargs)
        elif method == 'scgnn':
            return self._impute_scgnn(**kwargs)
        elif method == 'magic':
            return self._impute_magic(**kwargs)
        else:
            raise ValueError(f"Unknown imputation method: {method}")
            
    def _get_imputation_method(self):
        """Select imputation method based on overall method."""
        method_map = {
            'scnet': 'vae',
            'scgnn': 'scgnn', 
            'expimap': 'vae',
            'cefcon': 'vae',
            'auto': 'vae'
        }
        return method_map.get(self.method, 'vae')
        
    def _impute_vae(self, **kwargs):
        """VAE-based imputation."""
        if self.model is None:
            self.embed(method='vae')
            
        # Use trained VAE decoder for imputation
        self.model.eval()
        with torch.no_grad():
            X = torch.FloatTensor(self.adata.X.toarray() if hasattr(self.adata.X, 'toarray') else self.adata.X)
            recon_x, _, _, _ = self.model(X.to(self.device))
            self.imputed_data = recon_x.cpu().numpy()
            
        return self.imputed_data
        
    def _impute_scgnn(self, **kwargs):
        """scGNN-style imputation with graph regularization."""
        # Simplified scGNN imputation
        return self._impute_vae(**kwargs)
        
    def _impute_magic(self, **kwargs):
        """MAGIC imputation using scanpy."""
        import scanpy.external as sce
        sce.pp.magic(self.adata, **kwargs)
        self.imputed_data = self.adata.X.toarray() if hasattr(self.adata.X, 'toarray') else self.adata.X
        return self.imputed_data
        
    def infer_grn(self, method: str = 'cefcon', **kwargs):
        """
        Infer gene regulatory network.
        
        Parameters
        ----------
        method : str, default 'cefcon'
            GRN inference method: 'cefcon', 'genie3', 'scenic'
        **kwargs
            Method-specific parameters
            
        Returns
        -------
        grn : pd.DataFrame
            Gene regulatory network as edge list
        """
        if method == 'cefcon':
            return self._infer_grn_cefcon(**kwargs)
        elif method == 'genie3':
            return self._infer_grn_genie3(**kwargs)
        elif method == 'scenic':
            return self._infer_grn_scenic(**kwargs)
        else:
            raise ValueError(f"Unknown GRN inference method: {method}")
            
    def _infer_grn_cefcon(self, **kwargs):
        """CEFCON-style GRN inference using attention mechanisms."""
        # Simplified CEFCON implementation
        if self.embeddings is None:
            self.embed(method='gnn')
            
        # Use attention weights as regulatory relationships
        if hasattr(self.model, 'conv1') and hasattr(self.model.conv1, '_alpha'):
            attention_weights = self.model.conv1._alpha.cpu().numpy()
            
            # Convert attention to edge list
            gene_names = self.adata.var_names
            edges = []
            for i in range(len(gene_names)):
                for j in range(len(gene_names)):
                    if i != j and attention_weights[i, j] > 0.1:  # Threshold
                        edges.append({
                            'source': gene_names[i],
                            'target': gene_names[j], 
                            'weight': attention_weights[i, j]
                        })
                        
            self.grn = pd.DataFrame(edges)
        else:
            # Fallback: correlation-based GRN
            X = self.adata.X.toarray() if hasattr(self.adata.X, 'toarray') else self.adata.X
            corr_matrix = np.corrcoef(X.T)
            
            gene_names = self.adata.var_names
            edges = []
            for i in range(len(gene_names)):
                for j in range(i+1, len(gene_names)):
                    if abs(corr_matrix[i, j]) > 0.5:  # Threshold
                        edges.append({
                            'source': gene_names[i],
                            'target': gene_names[j],
                            'weight': corr_matrix[i, j]
                        })
                        
            self.grn = pd.DataFrame(edges)
            
        return self.grn
        
    def _infer_grn_genie3(self, **kwargs):
        """GENIE3-style GRN inference (placeholder)."""
        # Would implement GENIE3 algorithm
        return self._infer_grn_cefcon(**kwargs)
        
    def _infer_grn_scenic(self, **kwargs):
        """SCENIC GRN inference (placeholder)."""
        # Would implement SCENIC algorithm  
        return self._infer_grn_cefcon(**kwargs)
        
    def find_driver_genes(self, top_k: int = 50, **kwargs):
        """
        Identify driver genes/regulators.
        
        Parameters
        ----------
        top_k : int, default 50
            Number of top driver genes to return
        **kwargs
            Additional parameters
            
        Returns
        -------
        driver_genes : pd.DataFrame
            Top driver genes with influence scores
        """
        if self.grn is None:
            self.infer_grn()
            
        # Calculate influence scores based on network topology
        gene_influence = {}
        
        for _, edge in self.grn.iterrows():
            source = edge['source']
            weight = abs(edge['weight'])
            
            if source not in gene_influence:
                gene_influence[source] = 0
            gene_influence[source] += weight
            
        # Sort by influence score
        sorted_genes = sorted(gene_influence.items(), key=lambda x: x[1], reverse=True)
        
        driver_genes = pd.DataFrame(sorted_genes[:top_k], columns=['gene', 'influence_score'])
        return driver_genes
        
    def run_analysis(
        self,
        adata,
        steps: List[str] = ['preprocess', 'embed', 'cluster', 'impute'],
        **kwargs
    ):
        """
        Run complete analysis pipeline.
        
        Parameters
        ----------
        adata : anndata.AnnData
            Input single-cell data
        steps : list, default ['preprocess', 'embed', 'cluster', 'impute']
            Analysis steps to perform
        **kwargs
            Step-specific parameters
            
        Returns
        -------
        results : dict
            Dictionary containing results from each step
        """
        results = {}
        
        if 'preprocess' in steps:
            self.preprocess(adata, **kwargs.get('preprocess', {}))
            results['preprocessed_data'] = self.adata
            
        if 'embed' in steps:
            embeddings = self.embed(**kwargs.get('embed', {}))
            results['embeddings'] = embeddings
            
        if 'cluster' in steps:
            clusters = self.cluster(**kwargs.get('cluster', {}))
            results['clusters'] = clusters
            
        if 'impute' in steps:
            imputed = self.impute(**kwargs.get('impute', {}))
            results['imputed_data'] = imputed
            
        if 'grn' in steps:
            grn = self.infer_grn(**kwargs.get('grn', {}))
            results['grn'] = grn
            
        if 'drivers' in steps:
            drivers = self.find_driver_genes(**kwargs.get('drivers', {}))
            results['driver_genes'] = drivers
            
        return results
        
    def save_results(self, output_dir: str, **kwargs):
        """
        Save analysis results to files.
        
        Parameters
        ----------
        output_dir : str
            Output directory path
        **kwargs
            Additional save parameters
        """
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        if self.embeddings is not None:
            np.save(os.path.join(output_dir, 'embeddings.npy'), self.embeddings)
            
        if self.clusters is not None:
            pd.DataFrame({'cluster': self.clusters}).to_csv(
                os.path.join(output_dir, 'clusters.csv'), index=False
            )
            
        if self.imputed_data is not None:
            np.save(os.path.join(output_dir, 'imputed_data.npy'), self.imputed_data)
            
        if self.grn is not None:
            self.grn.to_csv(os.path.join(output_dir, 'grn.csv'), index=False)
            
        if self.adata is not None:
            self.adata.write(os.path.join(output_dir, 'adata.h5ad'))